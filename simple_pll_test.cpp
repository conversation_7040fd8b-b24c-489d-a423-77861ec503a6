// simple_pll_test.cpp
// 简化的数字锁相环测试程序
#include <Arduino.h>

// 仅包含必要的头文件进行测试
#include "digital_pll_control.h"

// 全局PLL控制器
DigitalPLLControl* pllController = nullptr;

void setup() {
    Serial.begin(115200);
    delay(1000);
    
    Serial.println("========================================");
    Serial.println("数字锁相环测试程序启动");
    Serial.println("========================================");
    
    // 创建PLL控制器
    pllController = new DigitalPLLControl(5);  // PWM通道5
    
    if (pllController) {
        pllController->begin();
        Serial.println("✅ 数字锁相环初始化完成");
        
        Serial.println("\n可用命令:");
        Serial.println("P15  - 设置15%功率");
        Serial.println("P25  - 设置25%功率");
        Serial.println("P0   - 关闭功率");
        Serial.println("S    - 显示PLL状态");
        Serial.println("R    - 重置PLL");
        Serial.println("T    - 运行测试");
        Serial.println("H    - 显示帮助");
        Serial.println("========================================");
    } else {
        Serial.println("❌ PLL控制器创建失败");
    }
}

void loop() {
    if (!pllController) return;
    
    // 更新PLL控制器
    pllController->update();
    
    // 处理串口命令
    if (Serial.available()) {
        String cmd = Serial.readString();
        cmd.trim();
        cmd.toUpperCase();
        
        if (cmd.startsWith("P")) {
            // 功率设置命令
            int power = cmd.substring(1).toInt();
            pllController->setPower(power);
            Serial.printf("✅ PLL功率设置: %d%%\n", power);
            
        } else if (cmd == "S") {
            // 状态显示
            Serial.println("========== PLL状态 ==========");
            Serial.printf("PLL锁定: %s\n", pllController->isPLLLocked() ? "是" : "否");
            Serial.printf("相位连接: %s\n", pllController->isPhaseConnected() ? "是" : "否");
            Serial.printf("当前功率: %d%%\n", pllController->getCurrentPower());
            Serial.printf("VCO频率: %.3fHz\n", pllController->getVCOFrequency());
            Serial.printf("相位误差: %.4f弧度\n", pllController->getPhaseError());
            Serial.printf("功率方差: %.2f%%\n", pllController->getPowerVariance());
            Serial.println("============================");
            
        } else if (cmd == "R") {
            // 重置PLL
            pllController->reset();
            Serial.println("✅ PLL已重置");
            
        } else if (cmd == "T") {
            // 测试序列
            Serial.println("🧪 开始PLL测试序列...");
            
            int powers[] = {0, 15, 25, 35, 25, 15, 0};
            for (int i = 0; i < 7; i++) {
                Serial.printf("测试功率: %d%%\n", powers[i]);
                pllController->setPower(powers[i]);
                delay(3000);  // 等待PLL稳定
                
                Serial.printf("  PLL锁定: %s\n", pllController->isPLLLocked() ? "是" : "否");
                Serial.printf("  VCO频率: %.3fHz\n", pllController->getVCOFrequency());
                Serial.printf("  相位误差: %.4f\n", pllController->getPhaseError());
            }
            Serial.println("✅ PLL测试完成");
            
        } else if (cmd == "D") {
            // 详细调试信息
            pllController->printDebugInfo();
            
        } else if (cmd == "H") {
            // 帮助信息
            Serial.println("========== PLL命令帮助 ==========");
            Serial.println("P<数字>  设置功率(如P15=15%)");
            Serial.println("S        显示PLL状态");
            Serial.println("R        重置PLL");
            Serial.println("T        运行测试序列");
            Serial.println("D        显示调试信息");
            Serial.println("H        显示此帮助");
            Serial.println("===============================");
            
        } else if (cmd.length() > 0) {
            Serial.println("❌ 未知命令，输入H查看帮助");
        }
    }
    
    // 定期状态报告
    static unsigned long lastReport = 0;
    if (millis() - lastReport > 30000) {  // 每30秒
        lastReport = millis();
        if (!pllController->isPLLLocked()) {
            Serial.println("⚠️  PLL未锁定，检查系统状态");
        }
    }
    
    delay(10);
}

/*
使用说明：

这个程序专门测试数字锁相环功能：

1. 编译并上传程序
2. 打开串口监视器(115200)
3. 输入命令测试：

基础测试：
P15  -> 设置15%功率
S    -> 查看PLL状态
P0   -> 关闭功率

PLL测试：
T    -> 运行完整测试序列
D    -> 查看详细调试信息
R    -> 重置PLL系统

观察指标：
- PLL锁定状态
- VCO频率稳定性
- 相位误差大小
- 功率方差

如果PLL工作正常，应该看到：
- PLL锁定: 是
- VCO频率: 约50.000Hz
- 相位误差: <0.1弧度
- 功率方差: <2%

这确认数字锁相环能够解决相位失联和功率漂移问题。
*/
