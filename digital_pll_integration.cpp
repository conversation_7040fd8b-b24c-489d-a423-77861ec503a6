// digital_pll_integration.cpp
// 数字锁相环系统集成实现
#include "digital_pll_integration.h"

// ============================================================================
// DigitalPLLIntegration 类实现
// ============================================================================

DigitalPLLIntegration::DigitalPLLIntegration() {
    pll_controller = nullptr;
    feedback_monitor = nullptr;
    correction_controller = nullptr;
    diagnostics = nullptr;
    
    system_start_time = millis();
    last_status_update = system_start_time;
    last_diagnostic_report = system_start_time;
}

DigitalPLLIntegration::~DigitalPLLIntegration() {
    if (pll_controller) delete pll_controller;
    if (feedback_monitor) delete feedback_monitor;
    if (correction_controller) delete correction_controller;
    if (diagnostics) delete diagnostics;
}

bool DigitalPLLIntegration::begin(int pwm_channel) {
    debugPrint("[PLL_INTEGRATION] 初始化数字锁相环系统...");
    
    // 创建核心组件
    pll_controller = new DigitalPLLControl(pwm_channel);
    if (!pll_controller) {
        debugPrint("[PLL_INTEGRATION] 错误：无法创建PLL控制器");
        return false;
    }
    
    feedback_monitor = new PowerFeedbackMonitor();
    if (!feedback_monitor) {
        debugPrint("[PLL_INTEGRATION] 错误：无法创建反馈监测器");
        return false;
    }
    
    correction_controller = new PowerCorrectionController(feedback_monitor);
    if (!correction_controller) {
        debugPrint("[PLL_INTEGRATION] 错误：无法创建校正控制器");
        return false;
    }
    
    diagnostics = new PLLDiagnostics(pll_controller);
    if (!diagnostics) {
        debugPrint("[PLL_INTEGRATION] 错误：无法创建诊断器");
        return false;
    }
    
    // 初始化各组件
    pll_controller->begin();
    feedback_monitor->begin();
    correction_controller->begin();
    
    system_status = PLL_SYSTEM_NORMAL;
    
    debugPrint("[PLL_INTEGRATION] 数字锁相环系统初始化完成");
    debugPrintf("[PLL_INTEGRATION] PWM通道: %d", pwm_channel);
    
    return true;
}

void DigitalPLLIntegration::update() {
    if (!system_enabled || !pll_controller || !feedback_monitor) {
        return;
    }
    
    // 更新核心组件
    pll_controller->update();
    
    // 更新功率反馈监测
    int current_power = pll_controller->getCurrentPower();
    feedback_monitor->update((float)current_power, (float)current_power);
    
    // 更新校正控制器
    if (correction_controller) {
        correction_controller->update();
    }
    
    // 更新诊断器
    if (diagnostics) {
        diagnostics->update();
    }
    
    // 更新系统状态
    updateSystemStatus();
    
    // 处理当前状态
    handleSystemState();
    
    // 定期诊断
    performDiagnostics();
    
    // 更新统计
    updateStatistics();
}

void DigitalPLLIntegration::setPower(int power_percent) {
    if (!system_enabled || !pll_controller) {
        return;
    }
    
    power_percent = constrain(power_percent, 0, 100);
    
    // 应用功率校正
    float corrected_power = power_percent;
    if (correction_controller && correction_controller->isAutoCorrectionEnabled()) {
        corrected_power = correction_controller->applyCorrectedPower((float)power_percent);
    }
    
    // 设置PLL控制器功率
    pll_controller->setPower((int)corrected_power);
    
    total_power_adjustments++;
    
    debugPrintf("[PLL_INTEGRATION] 功率设置: %d%% -> %.1f%% (校正后)", 
               power_percent, corrected_power);
}

int DigitalPLLIntegration::getCurrentPower() {
    if (!pll_controller) return 0;
    return pll_controller->getCurrentPower();
}

void DigitalPLLIntegration::updateSystemStatus() {
    unsigned long current_time = millis();
    
    if (current_time - last_status_update < status_update_interval) {
        return;
    }
    
    last_status_update = current_time;
    
    // 检查系统状态
    if (!pll_controller->isPLLLocked()) {
        if (system_status == PLL_SYSTEM_NORMAL) {
            system_status = PLL_SYSTEM_PHASE_LOSS;
            debugPrint("[PLL_INTEGRATION] 检测到相位失联");
        }
    } else if (feedback_monitor->isDrifting()) {
        if (system_status == PLL_SYSTEM_NORMAL) {
            system_status = PLL_SYSTEM_DRIFT_DETECTED;
            debugPrint("[PLL_INTEGRATION] 检测到功率漂移");
        }
    } else if (feedback_monitor->isPhaseLossDetected()) {
        system_status = PLL_SYSTEM_PHASE_LOSS;
        debugPrint("[PLL_INTEGRATION] 反馈监测检测到相位失联");
    } else {
        if (system_status != PLL_SYSTEM_NORMAL && system_status != PLL_SYSTEM_RECOVERY) {
            system_status = PLL_SYSTEM_NORMAL;
            debugPrint("[PLL_INTEGRATION] 系统状态恢复正常");
        }
    }
}

void DigitalPLLIntegration::handleSystemState() {
    switch (system_status) {
        case PLL_SYSTEM_NORMAL:
            handleNormalOperation();
            break;
            
        case PLL_SYSTEM_DRIFT_DETECTED:
            handleDriftDetection();
            break;
            
        case PLL_SYSTEM_PHASE_LOSS:
            handlePhaseLoss();
            break;
            
        case PLL_SYSTEM_RECOVERY:
            handleRecovery();
            break;
            
        case PLL_SYSTEM_ERROR:
            handleSystemError();
            break;
            
        default:
            break;
    }
}

void DigitalPLLIntegration::handleNormalOperation() {
    // 正常运行时的维护操作
    static unsigned long last_maintenance = 0;
    unsigned long current_time = millis();
    
    if (current_time - last_maintenance > 10000) { // 每10秒维护一次
        last_maintenance = current_time;
        
        // 检查系统健康状态
        if (feedback_monitor) {
            float stability_score = feedback_monitor->getStabilityScore();
            if (stability_score < 70.0f) {
                debugPrintf("[PLL_INTEGRATION] 稳定性评分较低: %.1f", stability_score);
            }
        }
    }
}

void DigitalPLLIntegration::handleDriftDetection() {
    debugPrint("[PLL_INTEGRATION] 处理功率漂移...");
    
    // 启用自动校正
    if (correction_controller && !correction_controller->isAutoCorrectionEnabled()) {
        correction_controller->enableAutoCorrection(true);
        debugPrint("[PLL_INTEGRATION] 启用自动功率校正");
    }
    
    // 如果漂移持续，考虑重置PLL
    static unsigned long drift_start_time = 0;
    if (drift_start_time == 0) {
        drift_start_time = millis();
    } else if (millis() - drift_start_time > 5000) { // 漂移持续5秒
        debugPrint("[PLL_INTEGRATION] 漂移持续时间过长，启动恢复程序");
        system_status = PLL_SYSTEM_RECOVERY;
        drift_start_time = 0;
    }
}

void DigitalPLLIntegration::handlePhaseLoss() {
    debugPrint("[PLL_INTEGRATION] 处理相位失联...");
    
    if (auto_recovery_enabled) {
        if (attemptAutoRecovery()) {
            system_status = PLL_SYSTEM_RECOVERY;
        } else {
            system_status = PLL_SYSTEM_ERROR;
            debugPrint("[PLL_INTEGRATION] 自动恢复失败，进入错误状态");
        }
    }
}

void DigitalPLLIntegration::handleRecovery() {
    static unsigned long recovery_start = 0;
    
    if (recovery_start == 0) {
        recovery_start = millis();
        executeRecoverySequence();
    }
    
    // 检查恢复是否成功
    if (pll_controller->isPLLLocked() && feedback_monitor->isStable()) {
        successful_recoveries++;
        system_status = PLL_SYSTEM_NORMAL;
        recovery_start = 0;
        debugPrint("[PLL_INTEGRATION] 恢复成功");
    } else if (millis() - recovery_start > 10000) { // 恢复超时10秒
        failed_recoveries++;
        system_status = PLL_SYSTEM_ERROR;
        recovery_start = 0;
        debugPrint("[PLL_INTEGRATION] 恢复超时，进入错误状态");
    }
}

void DigitalPLLIntegration::handleSystemError() {
    debugPrint("[PLL_INTEGRATION] 系统错误状态，尝试重置...");
    
    // 重置所有组件
    resetSystem();
    
    // 等待一段时间后重新尝试
    static unsigned long last_reset = 0;
    if (millis() - last_reset > 30000) { // 30秒后重试
        last_reset = millis();
        system_status = PLL_SYSTEM_INITIALIZING;
        debugPrint("[PLL_INTEGRATION] 重新初始化系统");
    }
}

bool DigitalPLLIntegration::attemptAutoRecovery() {
    debugPrint("[PLL_INTEGRATION] 尝试自动恢复...");
    
    // 重置PLL控制器
    if (pll_controller) {
        pll_controller->reset();
    }
    
    // 重置反馈监测器
    if (feedback_monitor) {
        feedback_monitor->reset();
    }
    
    // 重置校正控制器
    if (correction_controller) {
        correction_controller->resetCorrection();
    }
    
    return true;
}

void DigitalPLLIntegration::executeRecoverySequence() {
    debugPrint("[PLL_INTEGRATION] 执行恢复序列...");
    
    // 1. 降低功率到安全水平
    if (pll_controller) {
        int current_power = pll_controller->getCurrentPower();
        if (current_power > 20) {
            pll_controller->setPower(20); // 降低到20%
            debugPrint("[PLL_INTEGRATION] 降低功率到安全水平");
        }
    }
    
    // 2. 重新初始化PLL
    attemptAutoRecovery();
    
    // 3. 启用保守的校正参数
    if (correction_controller) {
        correction_controller->setCorrectionGain(0.05f); // 降低校正增益
        correction_controller->enableAutoCorrection(true);
    }
}

void DigitalPLLIntegration::performDiagnostics() {
    unsigned long current_time = millis();
    
    if (current_time - last_diagnostic_report < diagnostic_interval) {
        return;
    }
    
    last_diagnostic_report = current_time;
    
    if (debug_enabled && diagnostics) {
        debugPrint("[PLL_INTEGRATION] ========== 定期诊断报告 ==========");
        diagnostics->printReport();
        
        if (feedback_monitor) {
            feedback_monitor->printStatus();
        }
        
        if (correction_controller) {
            correction_controller->printCorrectionStatus();
        }
        
        debugPrint("[PLL_INTEGRATION] =====================================");
    }
}

void DigitalPLLIntegration::updateStatistics() {
    if (feedback_monitor) {
        float current_score = feedback_monitor->getStabilityScore();
        avg_stability_score = (avg_stability_score * 0.99f) + (current_score * 0.01f);
    }
}

PLLSystemStatus DigitalPLLIntegration::getSystemStatus() {
    return system_status;
}

bool DigitalPLLIntegration::isSystemStable() {
    return system_status == PLL_SYSTEM_NORMAL && 
           pll_controller && pll_controller->isPLLLocked() &&
           feedback_monitor && feedback_monitor->isStable();
}

bool DigitalPLLIntegration::isPLLLocked() {
    return pll_controller ? pll_controller->isPLLLocked() : false;
}

bool DigitalPLLIntegration::isPhaseConnected() {
    return pll_controller ? pll_controller->isPhaseConnected() : false;
}

float DigitalPLLIntegration::getStabilityScore() {
    return feedback_monitor ? feedback_monitor->getStabilityScore() : 0.0f;
}

float DigitalPLLIntegration::getPowerVariance() {
    return feedback_monitor ? feedback_monitor->getPowerVariance() : 0.0f;
}

float DigitalPLLIntegration::getVCOFrequency() {
    return pll_controller ? pll_controller->getVCOFrequency() : 50.0f;
}

void DigitalPLLIntegration::enableSystem(bool enable) {
    system_enabled = enable;
    debugPrintf("[PLL_INTEGRATION] 系统: %s", enable ? "启用" : "禁用");
}

void DigitalPLLIntegration::enableAutoRecovery(bool enable) {
    auto_recovery_enabled = enable;
    debugPrintf("[PLL_INTEGRATION] 自动恢复: %s", enable ? "启用" : "禁用");
}

void DigitalPLLIntegration::enableDebug(bool enable) {
    debug_enabled = enable;
    debugPrintf("[PLL_INTEGRATION] 调试输出: %s", enable ? "启用" : "禁用");
}

void DigitalPLLIntegration::resetSystem() {
    debugPrint("[PLL_INTEGRATION] 重置整个系统...");
    
    if (pll_controller) pll_controller->reset();
    if (feedback_monitor) feedback_monitor->reset();
    if (correction_controller) correction_controller->resetCorrection();
    if (diagnostics) diagnostics->resetStatistics();
    
    system_status = PLL_SYSTEM_NORMAL;
    total_power_adjustments = 0;
    
    debugPrint("[PLL_INTEGRATION] 系统重置完成");
}

void DigitalPLLIntegration::debugPrint(const char* message) {
    if (debug_enabled) {
        Serial.println(message);
    }
}

void DigitalPLLIntegration::debugPrintf(const char* format, ...) {
    if (debug_enabled) {
        va_list args;
        va_start(args, format);
        char buffer[256];
        vsnprintf(buffer, sizeof(buffer), format, args);
        va_end(args);
        Serial.println(buffer);
    }
}

void DigitalPLLIntegration::printSystemStatus() {
    const char* status_names[] = {
        "初始化中", "正常运行", "检测到漂移", "相位失联", "恢复中", "系统错误"
    };

    Serial.println("========== 数字锁相环系统状态 ==========");
    Serial.printf("系统状态: %s\n", status_names[system_status]);
    Serial.printf("系统启用: %s\n", system_enabled ? "是" : "否");
    Serial.printf("PLL锁定: %s\n", isPLLLocked() ? "是" : "否");
    Serial.printf("相位连接: %s\n", isPhaseConnected() ? "是" : "否");
    Serial.printf("系统稳定: %s\n", isSystemStable() ? "是" : "否");
    Serial.printf("当前功率: %d%%\n", getCurrentPower());
    Serial.printf("VCO频率: %.3f Hz\n", getVCOFrequency());
    Serial.printf("稳定性评分: %.1f/100\n", getStabilityScore());
    Serial.printf("功率方差: %.2f%%\n", getPowerVariance());
    Serial.printf("功率调整次数: %d\n", total_power_adjustments);
    Serial.printf("成功恢复次数: %d\n", successful_recoveries);
    Serial.printf("失败恢复次数: %d\n", failed_recoveries);

    unsigned long uptime = millis() - system_start_time;
    Serial.printf("系统运行时间: %lu 秒\n", uptime / 1000);
    Serial.println("========================================");
}

void DigitalPLLIntegration::printDetailedDiagnostics() {
    printSystemStatus();

    if (pll_controller) {
        pll_controller->printDebugInfo();
    }

    if (feedback_monitor) {
        feedback_monitor->printDetailedReport();
    }

    if (correction_controller) {
        correction_controller->printCorrectionStatus();
    }

    if (diagnostics) {
        diagnostics->printReport();
    }
}

// ============================================================================
// GlobalPLLInterface 类实现
// ============================================================================

DigitalPLLIntegration* GlobalPLLInterface::instance = nullptr;

bool GlobalPLLInterface::initialize(int pwm_channel) {
    if (instance) {
        delete instance;
    }

    instance = new DigitalPLLIntegration();
    if (!instance) {
        Serial.println("[GLOBAL_PLL] 错误：无法创建PLL集成实例");
        return false;
    }

    if (!instance->begin(pwm_channel)) {
        Serial.println("[GLOBAL_PLL] 错误：PLL系统初始化失败");
        delete instance;
        instance = nullptr;
        return false;
    }

    Serial.println("[GLOBAL_PLL] 全局PLL接口初始化成功");
    return true;
}

DigitalPLLIntegration* GlobalPLLInterface::getInstance() {
    return instance;
}

void GlobalPLLInterface::updatePLL() {
    if (instance) {
        instance->update();
    }
}

void GlobalPLLInterface::setPowerPLL(int power_percent) {
    if (instance) {
        instance->setPower(power_percent);
    }
}

int GlobalPLLInterface::getCurrentPowerPLL() {
    return instance ? instance->getCurrentPower() : 0;
}

bool GlobalPLLInterface::isPLLStable() {
    return instance ? instance->isSystemStable() : false;
}

void GlobalPLLInterface::printPLLStatus() {
    if (instance) {
        instance->printSystemStatus();
    }
}

void GlobalPLLInterface::resetPLL() {
    if (instance) {
        instance->resetSystem();
    }
}

void GlobalPLLInterface::cleanup() {
    if (instance) {
        delete instance;
        instance = nullptr;
        Serial.println("[GLOBAL_PLL] 全局PLL接口已清理");
    }
}

// ============================================================================
// 命令处理函数实现
// ============================================================================

void handlePLLTestCommand(String command) {
    if (!GlobalPLLInterface::getInstance()) {
        Serial.println("[PLL_TEST] 错误：PLL系统未初始化");
        return;
    }

    DigitalPLLIntegration* pll = GlobalPLLInterface::getInstance();

    if (command.startsWith("POWER,")) {
        int power = command.substring(6).toInt();
        pll->setPower(power);
        Serial.printf("[PLL_TEST] 设置功率: %d%%\n", power);
    }
    else if (command == "STATUS") {
        pll->printSystemStatus();
    }
    else if (command == "RESET") {
        pll->resetSystem();
        Serial.println("[PLL_TEST] 系统已重置");
    }
    else if (command == "RECOVERY") {
        pll->forceRecovery();
        Serial.println("[PLL_TEST] 强制恢复已启动");
    }
    else {
        Serial.println("[PLL_TEST] 未知测试命令");
        Serial.println("可用命令: POWER,<0-100> | STATUS | RESET | RECOVERY");
    }
}

void handlePLLDiagnosticCommand(String command) {
    if (!GlobalPLLInterface::getInstance()) {
        Serial.println("[PLL_DIAG] 错误：PLL系统未初始化");
        return;
    }

    DigitalPLLIntegration* pll = GlobalPLLInterface::getInstance();

    if (command == "FULL") {
        pll->printDetailedDiagnostics();
    }
    else if (command == "BRIEF") {
        pll->printSystemStatus();
    }
    else {
        Serial.println("[PLL_DIAG] 未知诊断命令");
        Serial.println("可用命令: FULL | BRIEF");
    }
}

void handlePLLConfigCommand(String command) {
    if (!GlobalPLLInterface::getInstance()) {
        Serial.println("[PLL_CONFIG] 错误：PLL系统未初始化");
        return;
    }

    DigitalPLLIntegration* pll = GlobalPLLInterface::getInstance();

    if (command.startsWith("DEBUG,")) {
        bool enable = command.substring(6).toInt() != 0;
        pll->enableDebug(enable);
        Serial.printf("[PLL_CONFIG] 调试输出: %s\n", enable ? "启用" : "禁用");
    }
    else if (command.startsWith("RECOVERY,")) {
        bool enable = command.substring(9).toInt() != 0;
        pll->enableAutoRecovery(enable);
        Serial.printf("[PLL_CONFIG] 自动恢复: %s\n", enable ? "启用" : "禁用");
    }
    else {
        Serial.println("[PLL_CONFIG] 未知配置命令");
        Serial.println("可用命令: DEBUG,<0|1> | RECOVERY,<0|1>");
    }
}

void parsePLLCommand(String msg) {
    if (msg.startsWith("PLLTEST,")) {
        handlePLLTestCommand(msg.substring(8));
    }
    else if (msg.startsWith("PLLDIAG,")) {
        handlePLLDiagnosticCommand(msg.substring(8));
    }
    else if (msg.startsWith("PLLCONFIG,")) {
        handlePLLConfigCommand(msg.substring(10));
    }
    else if (msg == "PLLHELP") {
        Serial.println("========== 数字锁相环命令帮助 ==========");
        Serial.println("PLLTEST,POWER,<0-100>  - 设置测试功率");
        Serial.println("PLLTEST,STATUS         - 显示系统状态");
        Serial.println("PLLTEST,RESET          - 重置系统");
        Serial.println("PLLTEST,RECOVERY       - 强制恢复");
        Serial.println("PLLDIAG,FULL           - 完整诊断报告");
        Serial.println("PLLDIAG,BRIEF          - 简要状态报告");
        Serial.println("PLLCONFIG,DEBUG,<0|1>  - 启用/禁用调试");
        Serial.println("PLLCONFIG,RECOVERY,<0|1> - 启用/禁用自动恢复");
        Serial.println("PLLHELP                - 显示此帮助");
        Serial.println("========================================");
    }
}
