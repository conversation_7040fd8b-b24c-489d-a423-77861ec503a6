// simple_1600w_test.cpp
// 简化的1600W发热丝测试程序 - 避免复杂的函数声明问题
#include <Arduino.h>
#include "optimized_pwm_control.h"

// 全局控制器
OptimizedPWMControl heaterController(5);

void setup() {
    Serial.begin(115200);
    delay(1000);
    
    Serial.println("========================================");
    Serial.println("1600W发热丝测试程序启动");
    Serial.println("========================================");
    
    // 配置1600W发热丝
    heaterController.setHeaterWattage(1600.0f);
    heaterController.setHeaterType(1600);
    heaterController.setSafetyMode(true);
    heaterController.setMaxSafePowerRatio(0.85f);
    
    // 初始化
    heaterController.begin();
    
    Serial.println("✅ 1600W发热丝系统初始化完成");
    heaterController.printHeaterConfig();
    
    Serial.println("\n可用命令:");
    Serial.println("P15  - 设置15%功率");
    Serial.println("P25  - 设置25%功率");
    Serial.println("P0   - 关闭功率");
    Serial.println("S    - 显示状态");
    Serial.println("T    - 运行测试");
    Serial.println("H    - 显示帮助");
    Serial.println("========================================");
}

void loop() {
    // 更新控制器
    heaterController.update();
    
    // 处理串口命令
    if (Serial.available()) {
        String cmd = Serial.readString();
        cmd.trim();
        cmd.toUpperCase();
        
        if (cmd.startsWith("P")) {
            // 功率设置命令：P15, P25, P0 等
            int power = cmd.substring(1).toInt();
            heaterController.setPower(power);
            
            // 计算实际功率
            float coeff = heaterController.linearizePWMPower(power);
            float watts = heaterController.getHeaterWattage() * coeff;
            
            Serial.printf("✅ 功率设置: %d%% -> %.0fW\n", power, watts);
            
        } else if (cmd == "S") {
            // 状态显示
            Serial.println("========== 系统状态 ==========");
            Serial.printf("发热丝: %.0fW\n", heaterController.getHeaterWattage());
            Serial.printf("当前功率: %d%%\n", heaterController.getCurrentPower());
            Serial.printf("安全模式: %s\n", heaterController.isSafetyModeEnabled() ? "启用" : "禁用");
            Serial.printf("运行时间: %lu秒\n", millis()/1000);
            Serial.println("=============================");
            
        } else if (cmd == "T") {
            // 测试序列
            Serial.println("🧪 开始测试序列...");
            
            int powers[] = {0, 10, 15, 20, 25, 15, 0};
            for (int i = 0; i < 7; i++) {
                Serial.printf("测试: %d%%\n", powers[i]);
                heaterController.setPower(powers[i]);
                delay(2000);
                
                float coeff = heaterController.linearizePWMPower(powers[i]);
                float watts = heaterController.getHeaterWattage() * coeff;
                Serial.printf("  结果: %d%% -> %.0fW\n", powers[i], watts);
            }
            Serial.println("✅ 测试完成");
            
        } else if (cmd == "H") {
            // 帮助信息
            Serial.println("========== 命令帮助 ==========");
            Serial.println("P<数字>  设置功率(如P15=15%)");
            Serial.println("S        显示系统状态");
            Serial.println("T        运行测试序列");
            Serial.println("H        显示此帮助");
            Serial.println("=============================");
            
        } else if (cmd.length() > 0) {
            Serial.println("❌ 未知命令，输入H查看帮助");
        }
    }
    
    delay(10);
}

/*
使用说明：

1. 编译并上传此程序
2. 打开串口监视器(115200)
3. 输入命令测试：

基础测试：
P15  -> 设置15%功率
S    -> 查看状态
P0   -> 关闭功率

完整测试：
T    -> 运行自动测试序列

这个程序专门测试1600W发热丝适配，
确认功率控制和安全保护是否正常工作。

如果测试成功，可以将heaterController集成到您的主程序中：

在PID控制部分：
// 原来：outPWM_update();
// 现在：heaterController.setPower(pid_output); heaterController.update();
*/
