// heater_integration_example.cpp
// 1600W发热丝集成示例代码
#include "optimized_pwm_control.h"

// 全局PWM控制器实例
OptimizedPWMControl pwmController(5);  // 使用PWM通道5

// 函数声明
void runHeater1600WTest();
void printHeater1600WHelp();

/**
 * 初始化1600W发热丝配置
 * 在setup()函数中调用
 */
void setup1600WHeater() {
    Serial.println("========== 初始化1600W发热丝系统 ==========");
    
    // 配置1600W发热丝
    pwmController.setHeaterWattage(1600.0f);
    pwmController.setHeaterType(1600);
    
    // 启用安全保护
    pwmController.setSafetyMode(true);
    pwmController.setMaxSafePowerRatio(0.85f);  // 最大85%功率保护
    
    // 优化控制参数
    pwmController.setSmoothingFactor(0.12f);    // 稍微降低平滑因子，提高响应
    pwmController.setPowerChangeLimit(3);       // 允许稍快的功率变化
    
    // 初始化PWM控制
    pwmController.begin();
    
    // 打印配置信息
    pwmController.printHeaterConfig();
    
    Serial.println("[HEATER_1600W] 1600W发热丝系统初始化完成");
    Serial.println("========================================");
}

/**
 * 1600W发热丝功率设置函数
 * 替换原有的功率设置逻辑
 */
void setHeater1600WPower(int power_percent) {
    // 输入验证
    power_percent = constrain(power_percent, 0, 100);
    
    // 记录功率变化
    static int last_power = -1;
    if (last_power != power_percent) {
        Serial.printf("[HEATER_1600W] 功率变化: %d%% -> %d%%\n", last_power, power_percent);
        last_power = power_percent;
    }
    
    // 设置功率（包含自动安全检查）
    pwmController.setPower(power_percent);
    
    // 可选：记录实际功率
    float actual_watts = pwmController.getHeaterWattage() * pwmController.linearizePWMPower(power_percent);
    Serial.printf("[HEATER_1600W] 设置%d%% -> 实际%.0fW\n", power_percent, actual_watts);
}

/**
 * 获取当前功率设置
 */
int getHeater1600WPower() {
    return pwmController.getCurrentPower();
}

/**
 * 检查发热丝状态
 */
bool isHeater1600WReady() {
    // 检查PWM控制器是否正常
    return pwmController.getCurrentPower() >= 0;  // 简单的状态检查
}

/**
 * 1600W发热丝串口命令处理
 * 在check_Serial()函数中添加
 */
void handleHeater1600WCommands(String command) {
    if (command.startsWith("H1600_POWER,")) {
        // 设置功率：H1600_POWER,25
        int power = command.substring(12).toInt();
        setHeater1600WPower(power);
        Serial.printf("1600W发热丝功率设置为: %d%%\n", power);
    }
    else if (command == "H1600_INFO") {
        // 显示发热丝信息
        pwmController.printHeaterConfig();
    }
    else if (command == "H1600_DEBUG") {
        // 显示调试信息
        pwmController.printDebugInfo();
    }
    else if (command == "H1600_TABLE") {
        // 显示线性化表
        pwmController.printLinearizationTable();
    }
    else if (command.startsWith("H1600_SAFETY,")) {
        // 安全模式控制：H1600_SAFETY,1
        bool enabled = command.substring(13).toInt() != 0;
        pwmController.setSafetyMode(enabled);
        Serial.printf("1600W发热丝安全模式: %s\n", enabled ? "启用" : "禁用");
    }
    else if (command.startsWith("H1600_LIMIT,")) {
        // 功率限制：H1600_LIMIT,80
        float limit = command.substring(12).toFloat() / 100.0f;
        pwmController.setMaxSafePowerRatio(limit);
        Serial.printf("1600W发热丝功率限制: %.1f%%\n", limit * 100);
    }
    else if (command == "H1600_TEST") {
        // 运行测试序列
        runHeater1600WTest();
    }
    else if (command == "H1600_HELP") {
        // 显示帮助
        printHeater1600WHelp();
    }
}

/**
 * 运行1600W发热丝测试序列
 */
void runHeater1600WTest() {
    Serial.println("[H1600_TEST] 开始1600W发热丝测试序列...");
    
    int test_powers[] = {0, 10, 15, 20, 25, 30, 20, 10, 0};
    int test_count = sizeof(test_powers) / sizeof(test_powers[0]);
    
    for (int i = 0; i < test_count; i++) {
        Serial.printf("[H1600_TEST] 测试功率: %d%%\n", test_powers[i]);
        setHeater1600WPower(test_powers[i]);
        
        // 等待稳定
        delay(3000);
        
        // 显示状态
        float actual_watts = pwmController.getHeaterWattage() * pwmController.linearizePWMPower(test_powers[i]);
        Serial.printf("[H1600_TEST] 结果 - 设置:%d%% 实际:%.0fW\n", test_powers[i], actual_watts);
        
        // 检查安全状态
        if (pwmController.isSafetyModeEnabled() && test_powers[i] > pwmController.getMaxSafePowerRatio() * 100) {
            Serial.printf("[H1600_TEST] 注意：功率被安全系统限制\n");
        }
    }
    
    Serial.println("[H1600_TEST] 测试序列完成");
    pwmController.printDebugInfo();
}

/**
 * 显示1600W发热丝命令帮助
 */
void printHeater1600WHelp() {
    Serial.println("========== 1600W发热丝命令帮助 ==========");
    Serial.println("H1600_POWER,<0-100>  - 设置功率百分比");
    Serial.println("H1600_INFO           - 显示发热丝配置信息");
    Serial.println("H1600_DEBUG          - 显示调试信息");
    Serial.println("H1600_TABLE          - 显示功率线性化表");
    Serial.println("H1600_SAFETY,<0|1>   - 启用/禁用安全模式");
    Serial.println("H1600_LIMIT,<50-100> - 设置最大功率限制");
    Serial.println("H1600_TEST           - 运行测试序列");
    Serial.println("H1600_HELP           - 显示此帮助");
    Serial.println("========================================");
}

/**
 * PID控制集成示例
 * 在您的PID控制代码中使用
 */
void integratePIDWith1600WHeater() {
    // 假设这是您的PID控制代码
    /*
    if (myPID.GetMode() == AUTOMATIC) {
        Input = beanTemperature;
        myPID.Compute();
        Output = constrain(Output, 0, 100);
        
        // 原来的代码：
        // heaterPowerLevel = Output;
        // outPWM_update();
        
        // 新的代码：使用1600W发热丝控制
        setHeater1600WPower((int)Output);
        
        // 可选：记录PID控制状态
        static unsigned long last_log = 0;
        if (millis() - last_log > 5000) {  // 每5秒记录一次
            last_log = millis();
            float actual_watts = pwmController.getHeaterWattage() * 
                               pwmController.linearizePWMPower((int)Output);
            Serial.printf("[PID_1600W] 温度:%.1f°C PID输出:%d%% 实际功率:%.0fW\n", 
                         beanTemperature, (int)Output, actual_watts);
        }
    }
    */
}

/**
 * 温度保护集成示例
 * 结合温度传感器实现多重保护
 */
void temperatureProtectionWith1600W(float current_temp, float target_temp) {
    // 温度过高保护
    if (current_temp > target_temp + 10.0f) {
        Serial.println("[TEMP_PROTECTION] 温度过高，降低功率");
        int current_power = getHeater1600WPower();
        setHeater1600WPower(max(0, current_power - 10));  // 降低10%功率
    }
    
    // 温度过低快速加热
    else if (current_temp < target_temp - 20.0f) {
        Serial.println("[TEMP_PROTECTION] 温度过低，适当提高功率");
        int current_power = getHeater1600WPower();
        setHeater1600WPower(min(50, current_power + 5));  // 最多提高到50%
    }
    
    // 紧急过热保护
    if (current_temp > 250.0f) {  // 假设250°C为紧急阈值
        Serial.println("[EMERGENCY] 紧急过热保护，关闭发热丝");
        setHeater1600WPower(0);
        pwmController.setSafetyMode(true);  // 强制启用安全模式
    }
}

/**
 * 主程序集成示例
 */
void setup() {
    Serial.begin(115200);
    
    // 初始化1600W发热丝
    setup1600WHeater();
    
    // 其他初始化代码...
}

void loop() {
    // 您的主循环代码...
    
    // 更新PWM控制（如果需要）
    pwmController.update();
    
    // 其他循环代码...
}

// 在check_Serial()函数中添加：
/*
void check_Serial() {
    if (Serial.available()) {
        String msg = Serial.readString();
        msg.trim();
        
        // 添加1600W发热丝命令处理
        if (msg.startsWith("H1600_")) {
            handleHeater1600WCommands(msg);
            return;
        }
        
        // 您的其他命令处理...
    }
}
*/

// 使用说明：
/*
1. 将此文件中的函数集成到您的主程序中
2. 在setup()中调用setup1600WHeater()
3. 用setHeater1600WPower()替换原有的功率设置
4. 在串口命令处理中添加handleHeater1600WCommands()
5. 可选：添加温度保护逻辑temperatureProtectionWith1600W()
*/
