// freertos_communication.cpp
// FreeRTOS通信接口层实现 - 串口、BLE、显示管理
#include "freertos_architecture_design.h"
#include <BluetoothSerial.h>

// ============================================================================
// 通信接口配置
// ============================================================================

// 串口配置
#define SERIAL_BUFFER_SIZE      512
#define COMMAND_TIMEOUT_MS      5000

// BLE配置
#define BLE_DEVICE_NAME         "CoffeeRoaster_FreeRTOS"
#define BLE_BUFFER_SIZE         256
#define BLE_NOTIFY_INTERVAL     1000  // 1秒通知间隔

// 显示配置
#define DISPLAY_UPDATE_ITEMS    8
#define DISPLAY_SCROLL_DELAY    2000

// 全局通信对象
BluetoothSerial SerialBT;

// 通信状态
struct CommStatus {
    bool serial_active = true;
    bool ble_connected = false;
    bool display_active = true;
    uint32_t serial_commands_processed = 0;
    uint32_t ble_commands_processed = 0;
    uint32_t display_updates = 0;
    uint32_t last_ble_activity = 0;
} comm_status;

// ============================================================================
// 串口通信任务实现
// ============================================================================

void Task_Serial_Comm(void *pvParameters) {
    char serial_buffer[SERIAL_BUFFER_SIZE];
    char command_buffer[256];
    uint16_t buffer_index = 0;
    
    Serial.println("[SERIAL] 串口通信任务启动 - 优先级1, 事件驱动");
    
    while(1) {
        // 1. 处理队列中的消息（来自其他任务的响应）
        if (xQueueReceive(xQueueSerialMsg, serial_buffer, 0) == pdTRUE) {
            if (xSemaphoreTake(xMutexSerial, pdMS_TO_TICKS(100)) == pdTRUE) {
                Serial.println(serial_buffer);
                xSemaphoreGive(xMutexSerial);
            }
        }
        
        // 2. 处理串口输入
        while (Serial.available()) {
            char c = Serial.read();
            
            if (c == '\n' || c == '\r') {
                if (buffer_index > 0) {
                    command_buffer[buffer_index] = '\0';
                    process_serial_command(command_buffer);
                    buffer_index = 0;
                }
            } else if (buffer_index < sizeof(command_buffer) - 1) {
                command_buffer[buffer_index++] = c;
            } else {
                // 缓冲区溢出，重置
                buffer_index = 0;
                Serial.println("[SERIAL] ⚠️ 命令缓冲区溢出");
            }
        }
        
        // 3. 定期发送系统状态
        static uint32_t last_status_send = 0;
        if (xTaskGetTickCount() - last_status_send > pdMS_TO_TICKS(10000)) { // 每10秒
            last_status_send = xTaskGetTickCount();
            send_system_status_serial();
        }
        
        vTaskDelay(pdMS_TO_TICKS(10)); // 10ms延迟
    }
}

void process_serial_command(const char* command) {
    String cmd = String(command);
    cmd.trim();
    cmd.toUpperCase();
    
    comm_status.serial_commands_processed++;
    
    Serial.printf("[SERIAL] 处理命令: %s\n", command);
    
    // FreeRTOS系统命令
    if (cmd == "STATUS") {
        send_system_status_serial();
    }
    else if (cmd == "TASKS") {
        System_Print_Task_Stats();
    }
    else if (cmd == "MEMORY") {
        System_Print_Memory_Stats();
    }
    else if (cmd == "REALTIME") {
        RealTime_Print_Status();
    }
    else if (cmd == "CONTROL") {
        Control_Print_Status();
    }
    else if (cmd == "DATAPROC") {
        DataProcessing_Print_Status();
    }
    else if (cmd.startsWith("PID,")) {
        // PID参数设置: PID,KP,2.0 或 PID,KI,0.5 或 PID,KD,1.0
        parse_pid_command(cmd);
    }
    else if (cmd.startsWith("POWER,")) {
        // 功率设置: POWER,25
        float power = cmd.substring(6).toFloat();
        System_Set_PWM_Power(power);
        Serial.printf("[SERIAL] 功率设置: %.1f%%\n", power);
    }
    else if (cmd.startsWith("TEMP,")) {
        // 温度设置: TEMP,200
        float temp = cmd.substring(5).toFloat();
        Control_Start_Roast(temp);
        Serial.printf("[SERIAL] 目标温度: %.1f°C\n", temp);
    }
    else if (cmd == "START") {
        Control_Start_Roast(200.0f); // 默认200°C
        Serial.println("[SERIAL] 开始烘焙");
    }
    else if (cmd == "STOP") {
        Control_Stop_Roast();
        Serial.println("[SERIAL] 停止烘焙");
    }
    else if (cmd == "EMERGENCY") {
        System_Emergency_Stop();
        Serial.println("[SERIAL] 紧急停止");
    }
    else if (cmd == "RESET") {
        RealTime_Set_Emergency_Stop(false);
        Serial.println("[SERIAL] 重置紧急停止");
    }
    else if (cmd == "HELP") {
        print_serial_help();
    }
    else {
        Serial.printf("[SERIAL] 未知命令: %s\n", command);
        Serial.println("输入 HELP 查看可用命令");
    }
}

void parse_pid_command(const String& cmd) {
    // 解析PID命令: PID,KP,2.0
    int first_comma = cmd.indexOf(',');
    int second_comma = cmd.indexOf(',', first_comma + 1);
    
    if (first_comma > 0 && second_comma > 0) {
        String param = cmd.substring(first_comma + 1, second_comma);
        float value = cmd.substring(second_comma + 1).toFloat();
        
        if (param == "KP") {
            Control_Set_PID_Parameters(value, Ki, Kd);
            Serial.printf("[SERIAL] PID Kp设置: %.3f\n", value);
        }
        else if (param == "KI") {
            Control_Set_PID_Parameters(Kp, value, Kd);
            Serial.printf("[SERIAL] PID Ki设置: %.3f\n", value);
        }
        else if (param == "KD") {
            Control_Set_PID_Parameters(Kp, Ki, value);
            Serial.printf("[SERIAL] PID Kd设置: %.3f\n", value);
        }
    }
}

void send_system_status_serial(void) {
    Serial.println("========== 系统状态报告 ==========");
    Serial.printf("运行时间: %lu 秒\n", xTaskGetTickCount() / 1000);
    Serial.printf("可用内存: %d bytes\n", esp_get_free_heap_size());
    
    // 获取传感器数据
    SensorData_t sensor_data;
    if (System_Get_Sensor_Data(&sensor_data, pdMS_TO_TICKS(100))) {
        Serial.printf("豆温: %.1f°C\n", sensor_data.bean_temperature);
        Serial.printf("环温: %.1f°C\n", sensor_data.env_temperature);
        Serial.printf("电位器: %.1f%%\n", sensor_data.pot_value);
    }
    
    Serial.printf("当前功率: %.1f%%\n", RealTime_Get_Current_Power());
    Serial.printf("安全状态: %s\n", RealTime_Check_Safety_Status() ? "正常" : "紧急停止");
    Serial.printf("串口命令: %lu\n", comm_status.serial_commands_processed);
    Serial.printf("BLE连接: %s\n", comm_status.ble_connected ? "已连接" : "未连接");
    Serial.println("===============================");
}

void print_serial_help(void) {
    Serial.println("========== FreeRTOS串口命令 ==========");
    Serial.println("系统命令:");
    Serial.println("  STATUS          - 系统状态");
    Serial.println("  TASKS           - 任务状态");
    Serial.println("  MEMORY          - 内存状态");
    Serial.println("  REALTIME        - 实时控制状态");
    Serial.println("  CONTROL         - 控制算法状态");
    Serial.println("  DATAPROC        - 数据处理状态");
    Serial.println("控制命令:");
    Serial.println("  POWER,<0-100>   - 设置功率");
    Serial.println("  TEMP,<温度>     - 设置目标温度");
    Serial.println("  START           - 开始烘焙");
    Serial.println("  STOP            - 停止烘焙");
    Serial.println("  EMERGENCY       - 紧急停止");
    Serial.println("  RESET           - 重置紧急停止");
    Serial.println("PID命令:");
    Serial.println("  PID,KP,<值>     - 设置比例增益");
    Serial.println("  PID,KI,<值>     - 设置积分增益");
    Serial.println("  PID,KD,<值>     - 设置微分增益");
    Serial.println("====================================");
}

// ============================================================================
// BLE通信任务实现
// ============================================================================

void Task_BLE_Comm(void *pvParameters) {
    char ble_buffer[BLE_BUFFER_SIZE];
    
    Serial.println("[BLE] BLE通信任务启动 - 优先级1, 事件驱动");
    
    // 初始化BLE
    if (!SerialBT.begin(BLE_DEVICE_NAME)) {
        Serial.println("[BLE] ❌ BLE初始化失败");
        vTaskDelete(NULL);
        return;
    }
    
    Serial.printf("[BLE] ✅ BLE已启动: %s\n", BLE_DEVICE_NAME);
    
    while(1) {
        // 1. 检查BLE连接状态
        bool currently_connected = SerialBT.hasClient();
        if (currently_connected != comm_status.ble_connected) {
            comm_status.ble_connected = currently_connected;
            if (currently_connected) {
                Serial.println("[BLE] 客户端已连接");
                xEventGroupSetBits(xEventGroupSystem, EVENT_BIT_BLE_CONNECTED);
                send_welcome_message_ble();
            } else {
                Serial.println("[BLE] 客户端已断开");
                xEventGroupClearBits(xEventGroupSystem, EVENT_BIT_BLE_CONNECTED);
            }
        }
        
        // 2. 处理BLE命令
        if (comm_status.ble_connected && SerialBT.available()) {
            String command = SerialBT.readString();
            command.trim();
            
            comm_status.ble_commands_processed++;
            comm_status.last_ble_activity = xTaskGetTickCount();
            
            Serial.printf("[BLE] 收到命令: %s\n", command.c_str());
            
            // 处理BLE命令（复用串口命令处理逻辑）
            process_ble_command(command);
        }
        
        // 3. 定期发送数据通知
        static uint32_t last_notify = 0;
        if (comm_status.ble_connected && 
            xTaskGetTickCount() - last_notify > pdMS_TO_TICKS(BLE_NOTIFY_INTERVAL)) {
            last_notify = xTaskGetTickCount();
            send_data_notification_ble();
        }
        
        // 4. 连接超时检查
        if (comm_status.ble_connected && 
            xTaskGetTickCount() - comm_status.last_ble_activity > pdMS_TO_TICKS(300000)) { // 5分钟超时
            Serial.println("[BLE] ⚠️ 连接超时，断开BLE");
            SerialBT.disconnect();
        }
        
        vTaskDelay(pdMS_TO_TICKS(100)); // 100ms延迟
    }
}

void process_ble_command(const String& command) {
    // BLE命令处理（可以与串口命令不同）
    if (command == "STATUS") {
        send_json_status_ble();
    }
    else if (command.startsWith("POWER:")) {
        float power = command.substring(6).toFloat();
        System_Set_PWM_Power(power);
        SerialBT.printf("OK:POWER:%.1f\n", power);
    }
    else if (command.startsWith("TEMP:")) {
        float temp = command.substring(5).toFloat();
        Control_Start_Roast(temp);
        SerialBT.printf("OK:TEMP:%.1f\n", temp);
    }
    else if (command == "START") {
        Control_Start_Roast(200.0f);
        SerialBT.println("OK:START");
    }
    else if (command == "STOP") {
        Control_Stop_Roast();
        SerialBT.println("OK:STOP");
    }
    else {
        SerialBT.printf("ERROR:UNKNOWN:%s\n", command.c_str());
    }
}

void send_welcome_message_ble(void) {
    SerialBT.println("WELCOME:CoffeeRoaster_FreeRTOS");
    SerialBT.println("VERSION:2.0");
    SerialBT.println("READY");
}

void send_json_status_ble(void) {
    // 发送JSON格式的状态数据
    SensorData_t sensor_data;
    if (System_Get_Sensor_Data(&sensor_data, pdMS_TO_TICKS(100))) {
        SerialBT.printf("{\"bean_temp\":%.1f,\"env_temp\":%.1f,\"power\":%.1f,\"time\":%lu}\n",
                       sensor_data.bean_temperature,
                       sensor_data.env_temperature,
                       RealTime_Get_Current_Power(),
                       xTaskGetTickCount() / 1000);
    }
}

void send_data_notification_ble(void) {
    // 定期数据通知
    SensorData_t sensor_data;
    if (System_Get_Sensor_Data(&sensor_data, pdMS_TO_TICKS(50))) {
        SerialBT.printf("DATA:%.1f:%.1f:%.1f:%lu\n",
                       sensor_data.bean_temperature,
                       sensor_data.env_temperature,
                       RealTime_Get_Current_Power(),
                       xTaskGetTickCount() / 1000);
    }
}

// ============================================================================
// 显示管理任务实现
// ============================================================================

void Task_Display_Mgr(void *pvParameters) {
    TickType_t xLastWakeTime = xTaskGetTickCount();
    DisplayData_t display_data;
    
    Serial.println("[DISPLAY] 显示管理任务启动 - 优先级1, 1Hz");
    
    // 初始化显示
    if (!init_display()) {
        Serial.println("[DISPLAY] ⚠️ 显示初始化失败，继续运行");
        // 不删除任务，继续处理数据但不显示
    }
    
    uint8_t display_page = 0;
    uint32_t page_switch_time = 0;
    
    while(1) {
        // 1. 获取显示数据
        if (xQueueReceive(xQueueDisplayData, &display_data, pdMS_TO_TICKS(100)) == pdTRUE) {
            
            comm_status.display_updates++;
            
            // 2. 根据当前页面显示不同内容
            switch (display_page) {
                case 0: // 主页面 - 温度和功率
                    display_main_page(&display_data);
                    break;
                    
                case 1: // 系统状态页面
                    display_system_page();
                    break;
                    
                case 2: // PID参数页面
                    display_pid_page();
                    break;
                    
                case 3: // 网络状态页面
                    display_network_page();
                    break;
                    
                default:
                    display_page = 0;
                    break;
            }
            
            // 3. 自动切换页面
            if (xTaskGetTickCount() - page_switch_time > pdMS_TO_TICKS(DISPLAY_SCROLL_DELAY)) {
                page_switch_time = xTaskGetTickCount();
                display_page = (display_page + 1) % 4;
            }
        }
        
        // 4. 显示状态指示
        update_status_indicators();
        
        vTaskDelayUntil(&xLastWakeTime, pdMS_TO_TICKS(CYCLE_DISPLAY_MGR));
    }
}

bool init_display(void) {
    // 初始化显示硬件（I2C LCD、OLED等）
    if (xSemaphoreTake(xMutexI2C, pdMS_TO_TICKS(1000)) == pdTRUE) {
        // 这里添加实际的显示初始化代码
        // Wire.begin();
        // lcd.init();
        // lcd.backlight();
        
        xSemaphoreGive(xMutexI2C);
        Serial.println("[DISPLAY] ✅ 显示硬件初始化完成");
        return true;
    }
    
    return false;
}

void display_main_page(const DisplayData_t* data) {
    if (xSemaphoreTake(xMutexI2C, pdMS_TO_TICKS(100)) == pdTRUE) {
        // 显示主要信息
        // 第1行：豆温和环温
        // 第2行：功率和时间
        // 第3行：状态信息
        // 第4行：滚动消息
        
        Serial.printf("[DISPLAY] 主页面 - 豆温:%.1f°C 环温:%.1f°C 功率:%.1f%% 时间:%lus\n",
                     data->bean_temp, data->env_temp, data->power_percent, data->roast_time);
        
        xSemaphoreGive(xMutexI2C);
    }
}

void display_system_page(void) {
    if (xSemaphoreTake(xMutexI2C, pdMS_TO_TICKS(100)) == pdTRUE) {
        // 显示系统信息
        Serial.printf("[DISPLAY] 系统页面 - 内存:%d 任务:%d 运行:%lus\n",
                     esp_get_free_heap_size(), uxTaskGetNumberOfTasks(), xTaskGetTickCount() / 1000);
        
        xSemaphoreGive(xMutexI2C);
    }
}

void display_pid_page(void) {
    if (xSemaphoreTake(xMutexI2C, pdMS_TO_TICKS(100)) == pdTRUE) {
        // 显示PID参数
        Serial.printf("[DISPLAY] PID页面 - Kp:%.2f Ki:%.2f Kd:%.2f\n", Kp, Ki, Kd);
        
        xSemaphoreGive(xMutexI2C);
    }
}

void display_network_page(void) {
    if (xSemaphoreTake(xMutexI2C, pdMS_TO_TICKS(100)) == pdTRUE) {
        // 显示网络状态
        Serial.printf("[DISPLAY] 网络页面 - BLE:%s 串口:%lu BLE:%lu\n",
                     comm_status.ble_connected ? "连接" : "断开",
                     comm_status.serial_commands_processed,
                     comm_status.ble_commands_processed);
        
        xSemaphoreGive(xMutexI2C);
    }
}

void update_status_indicators(void) {
    // 更新LED指示灯等状态指示器
    static bool led_state = false;
    led_state = !led_state;
    
    // 安全状态指示
    bool safety_ok = RealTime_Check_Safety_Status();
    // digitalWrite(SAFETY_LED_PIN, safety_ok ? HIGH : LOW);
    
    // BLE连接指示
    // digitalWrite(BLE_LED_PIN, comm_status.ble_connected ? HIGH : LOW);
    
    // 心跳指示
    // digitalWrite(HEARTBEAT_LED_PIN, led_state ? HIGH : LOW);
}

// ============================================================================
// 通信层工具函数
// ============================================================================

void Communication_Print_Status(void) {
    Serial.println("========== 通信接口层状态 ==========");
    Serial.printf("串口活动: %s\n", comm_status.serial_active ? "是" : "否");
    Serial.printf("BLE连接: %s\n", comm_status.ble_connected ? "是" : "否");
    Serial.printf("显示活动: %s\n", comm_status.display_active ? "是" : "否");
    Serial.printf("串口命令: %lu\n", comm_status.serial_commands_processed);
    Serial.printf("BLE命令: %lu\n", comm_status.ble_commands_processed);
    Serial.printf("显示更新: %lu\n", comm_status.display_updates);
    
    Serial.printf("串口队列: %d/%d\n", 
                 uxQueueMessagesWaiting(xQueueSerialMsg), QUEUE_SIZE_SERIAL_MSG);
    Serial.printf("BLE队列: %d/%d\n", 
                 uxQueueMessagesWaiting(xQueueBLEMsg), QUEUE_SIZE_BLE_MSG);
    Serial.printf("显示队列: %d/%d\n", 
                 uxQueueMessagesWaiting(xQueueDisplayData), QUEUE_SIZE_DISPLAY_DATA);
    
    Serial.println("==================================");
}
