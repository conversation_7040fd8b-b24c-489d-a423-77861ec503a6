// power_feedback_monitor.h
// 功率反馈监测系统 - 检测功率漂移和相位失联
#ifndef POWER_FEEDBACK_MONITOR_H
#define POWER_FEEDBACK_MONITOR_H

#include <Arduino.h>
#include <math.h>

// 监测参数
#define FEEDBACK_SAMPLE_RATE 100          // 反馈采样率100Hz
#define POWER_HISTORY_SIZE 50             // 功率历史记录大小
#define DRIFT_DETECTION_WINDOW 20         // 漂移检测窗口
#define STABILITY_THRESHOLD 2.0f          // 稳定性阈值
#define DRIFT_ALERT_THRESHOLD 5.0f        // 漂移报警阈值
#define PHASE_LOSS_TIMEOUT 200            // 相位失联超时200ms

// 反馈监测状态
enum FeedbackStatus {
    FEEDBACK_STABLE,                      // 稳定
    FEEDBACK_MINOR_DRIFT,                 // 轻微漂移
    FEEDBACK_MAJOR_DRIFT,                 // 严重漂移
    FEEDBACK_PHASE_LOSS,                  // 相位失联
    FEEDBACK_SYSTEM_ERROR                 // 系统错误
};

/**
 * 功率反馈监测器
 * 实时监测功率输出稳定性，检测异常漂移和相位失联
 */
class PowerFeedbackMonitor {
private:
    // 功率监测数据
    float power_history[POWER_HISTORY_SIZE];     // 功率历史记录
    int history_index = 0;                       // 历史记录索引
    float target_power = 0.0f;                   // 目标功率
    float actual_power = 0.0f;                   // 实际功率
    
    // 统计分析
    float power_mean = 0.0f;                     // 功率均值
    float power_variance = 0.0f;                 // 功率方差
    float power_trend = 0.0f;                    // 功率趋势
    float drift_rate = 0.0f;                     // 漂移速率
    
    // 状态监测
    FeedbackStatus current_status = FEEDBACK_STABLE;
    unsigned long last_stable_time = 0;          // 上次稳定时间
    unsigned long last_update_time = 0;          // 上次更新时间
    bool drift_alert_active = false;             // 漂移报警状态
    
    // 异常检测
    int consecutive_drift_count = 0;             // 连续漂移计数
    int phase_loss_count = 0;                    // 相位失联计数
    float max_drift_detected = 0.0f;            // 检测到的最大漂移
    
    // 自适应阈值
    float adaptive_threshold = STABILITY_THRESHOLD;
    float threshold_adjustment_factor = 0.1f;
    
public:
    PowerFeedbackMonitor();
    
    // 初始化监测器
    void begin();
    
    // 更新监测数据
    void update(float target, float actual);
    
    // 获取监测状态
    FeedbackStatus getStatus();
    bool isStable();
    bool isDrifting();
    bool isPhaseLossDetected();
    
    // 获取统计数据
    float getPowerVariance();
    float getDriftRate();
    float getPowerTrend();
    float getStabilityScore();
    
    // 控制功能
    void reset();
    void setThreshold(float threshold);
    void enableAdaptiveThreshold(bool enable);
    
    // 诊断和调试
    void printStatus();
    void printDetailedReport();
    
private:
    // 核心分析算法
    void updateStatistics();
    void detectDrift();
    void detectPhaseLoss();
    void updateAdaptiveThreshold();
    
    // 数学计算
    float calculateMean();
    float calculateVariance();
    float calculateTrend();
    float calculateDriftRate();
    
    // 状态管理
    void updateStatus();
    void handleDriftAlert();
    void handlePhaseAlert();
};

/**
 * 功率校正控制器
 * 基于反馈监测结果自动校正功率输出
 */
class PowerCorrectionController {
private:
    PowerFeedbackMonitor* monitor;
    
    // 校正参数
    float correction_gain = 0.1f;                // 校正增益
    float max_correction = 10.0f;                // 最大校正幅度
    bool auto_correction_enabled = true;         // 自动校正开关
    
    // 校正历史
    float correction_history[10];                // 校正历史记录
    int correction_index = 0;                    // 校正索引
    float total_correction = 0.0f;               // 累计校正量
    
    // 安全保护
    int correction_count = 0;                    // 校正次数
    unsigned long last_correction_time = 0;      // 上次校正时间
    bool safety_mode = false;                    // 安全模式
    
public:
    PowerCorrectionController(PowerFeedbackMonitor* feedback_monitor);
    
    // 初始化校正器
    void begin();
    
    // 主更新函数
    void update();
    
    // 计算功率校正
    float calculateCorrection(float target_power, float actual_power);
    
    // 应用校正
    float applyCorrectedPower(float original_power);
    
    // 控制功能
    void enableAutoCorrection(bool enable);
    void setCorrectionGain(float gain);
    void resetCorrection();
    void enterSafetyMode();
    void exitSafetyMode();
    
    // 状态查询
    bool isAutoCorrectionEnabled();
    bool isInSafetyMode();
    float getTotalCorrection();
    
    // 诊断
    void printCorrectionStatus();
    
private:
    // 校正算法
    float pidCorrection(float error);
    float adaptiveCorrection(float error);
    
    // 安全检查
    bool isCorrectionSafe(float correction);
    void updateSafetyMode();
};

/**
 * 相位失联恢复器
 * 检测到相位失联时自动执行恢复程序
 */
class PhaseLossRecovery {
private:
    PowerFeedbackMonitor* monitor;
    
    // 恢复状态
    bool recovery_active = false;                // 恢复程序激活
    int recovery_step = 0;                       // 恢复步骤
    unsigned long recovery_start_time = 0;       // 恢复开始时间
    
    // 恢复策略
    float recovery_power_levels[5] = {0, 10, 25, 50, 75}; // 恢复功率序列
    int recovery_delays[5] = {1000, 2000, 3000, 2000, 1000}; // 恢复延迟序列
    
    // 恢复统计
    int recovery_attempts = 0;                   // 恢复尝试次数
    int successful_recoveries = 0;               // 成功恢复次数
    float avg_recovery_time = 0.0f;              // 平均恢复时间
    
public:
    PhaseLossRecovery(PowerFeedbackMonitor* feedback_monitor);
    
    // 初始化恢复器
    void begin();
    
    // 主更新函数
    void update();
    
    // 启动恢复程序
    void startRecovery();
    
    // 停止恢复程序
    void stopRecovery();
    
    // 状态查询
    bool isRecoveryActive();
    int getCurrentStep();
    float getRecoveryProgress();
    
    // 统计信息
    float getSuccessRate();
    float getAverageRecoveryTime();
    
    // 诊断
    void printRecoveryStatus();
    
private:
    // 恢复步骤
    void executeRecoveryStep();
    void checkRecoverySuccess();
    void finalizeRecovery(bool success);
};

#endif // POWER_FEEDBACK_MONITOR_H
