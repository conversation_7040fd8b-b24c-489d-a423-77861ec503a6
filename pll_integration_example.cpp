// pll_integration_example.cpp
// 数字锁相环集成示例 - 展示如何在现有代码中集成
#include "digital_pll_integration.h"
#include "pll_protection_system.h"

// 全局变量
DigitalPLLIntegration* g_pll_system = nullptr;
ProtectionController* g_protection_system = nullptr;

/**
 * 初始化数字锁相环系统
 * 替换原有的相位控制初始化
 */
bool initializeDigitalPLL(int pwm_channel = 5) {
    Serial.println("========== 初始化数字锁相环系统 ==========");
    
    // 创建PLL集成系统
    g_pll_system = new DigitalPLLIntegration();
    if (!g_pll_system) {
        Serial.println("[PLL_INIT] 错误：无法创建PLL系统");
        return false;
    }
    
    // 初始化PLL系统
    if (!g_pll_system->begin(pwm_channel)) {
        Serial.println("[PLL_INIT] 错误：PLL系统初始化失败");
        delete g_pll_system;
        g_pll_system = nullptr;
        return false;
    }
    
    // 创建保护系统
    g_protection_system = new ProtectionController();
    if (!g_protection_system) {
        Serial.println("[PLL_INIT] 警告：无法创建保护系统");
    } else {
        if (!g_protection_system->begin()) {
            Serial.println("[PLL_INIT] 警告：保护系统初始化失败");
            delete g_protection_system;
            g_protection_system = nullptr;
        }
    }
    
    Serial.println("[PLL_INIT] 数字锁相环系统初始化完成");
    Serial.printf("[PLL_INIT] PWM通道: %d\n", pwm_channel);
    Serial.printf("[PLL_INIT] 保护系统: %s\n", g_protection_system ? "启用" : "禁用");
    
    return true;
}

/**
 * 更新数字锁相环系统
 * 替换原有的 outPWM_update() 中的相位控制部分
 */
void updateDigitalPLL() {
    if (!g_pll_system) return;
    
    // 更新PLL系统
    g_pll_system->update();
    
    // 更新保护系统
    if (g_protection_system) {
        // 这里需要获取反馈监测器的引用
        // 为了简化示例，我们传递nullptr
        g_protection_system->update(nullptr, nullptr);
    }
}

/**
 * 设置功率
 * 替换原有的功率设置逻辑
 */
void setDigitalPLLPower(int power_percent) {
    if (!g_pll_system) return;
    
    // 安全检查
    power_percent = constrain(power_percent, 0, 100);
    
    // 检查保护系统状态
    if (g_protection_system && g_protection_system->isEmergencyShutdown()) {
        Serial.println("[PLL_POWER] 警告：系统处于紧急关闭状态，无法设置功率");
        return;
    }
    
    // 设置功率
    g_pll_system->setPower(power_percent);
    
    Serial.printf("[PLL_POWER] 功率设置: %d%%\n", power_percent);
}

/**
 * 获取当前功率
 */
int getDigitalPLLPower() {
    if (!g_pll_system) return 0;
    return g_pll_system->getCurrentPower();
}

/**
 * 检查系统状态
 */
bool isDigitalPLLStable() {
    if (!g_pll_system) return false;
    return g_pll_system->isSystemStable();
}

/**
 * 打印系统状态
 */
void printDigitalPLLStatus() {
    if (!g_pll_system) {
        Serial.println("[PLL_STATUS] 数字锁相环系统未初始化");
        return;
    }
    
    g_pll_system->printSystemStatus();
    
    if (g_protection_system) {
        g_protection_system->printProtectionStatus();
    }
}

/**
 * 重置系统
 */
void resetDigitalPLL() {
    if (g_pll_system) {
        g_pll_system->resetSystem();
    }
    
    if (g_protection_system) {
        g_protection_system->resetProtection();
    }
    
    Serial.println("[PLL_RESET] 数字锁相环系统已重置");
}

/**
 * 处理PID控制输出
 * 在您的PID控制代码中调用此函数
 */
void handlePIDOutput(float pid_output) {
    // 将PID输出转换为功率百分比
    int power_percent = (int)constrain(pid_output, 0, 100);
    
    // 使用数字锁相环设置功率
    setDigitalPLLPower(power_percent);
    
    // 可选：记录PID控制状态
    static unsigned long last_log_time = 0;
    if (millis() - last_log_time > 5000) { // 每5秒记录一次
        last_log_time = millis();
        Serial.printf("[PID_PLL] PID输出: %.2f -> 功率: %d%% | 系统稳定: %s\n", 
                     pid_output, power_percent, isDigitalPLLStable() ? "是" : "否");
    }
}

/**
 * 串口命令处理示例
 * 在您的 check_Serial() 函数中添加这些命令
 */
void handleDigitalPLLCommands(String command) {
    if (command.startsWith("DPLL_INIT")) {
        // 初始化数字锁相环
        int channel = 5;
        if (command.length() > 9) {
            channel = command.substring(10).toInt();
        }
        
        if (initializeDigitalPLL(channel)) {
            Serial.println("数字锁相环初始化成功");
        } else {
            Serial.println("数字锁相环初始化失败");
        }
    }
    else if (command.startsWith("DPLL_POWER,")) {
        // 设置功率
        int power = command.substring(11).toInt();
        setDigitalPLLPower(power);
    }
    else if (command == "DPLL_STATUS") {
        // 显示状态
        printDigitalPLLStatus();
    }
    else if (command == "DPLL_RESET") {
        // 重置系统
        resetDigitalPLL();
    }
    else if (command == "DPLL_TEST") {
        // 运行测试序列
        runDigitalPLLTest();
    }
    else if (command == "DPLL_HELP") {
        // 显示帮助
        Serial.println("========== 数字锁相环命令帮助 ==========");
        Serial.println("DPLL_INIT[,通道]    - 初始化系统");
        Serial.println("DPLL_POWER,<0-100>  - 设置功率");
        Serial.println("DPLL_STATUS         - 显示状态");
        Serial.println("DPLL_RESET          - 重置系统");
        Serial.println("DPLL_TEST           - 运行测试");
        Serial.println("DPLL_HELP           - 显示帮助");
        Serial.println("========================================");
    }
}

/**
 * 运行简单的测试序列
 */
void runDigitalPLLTest() {
    if (!g_pll_system) {
        Serial.println("[PLL_TEST] 错误：系统未初始化");
        return;
    }
    
    Serial.println("[PLL_TEST] 开始数字锁相环测试序列...");
    
    int test_powers[] = {0, 25, 50, 75, 100, 50, 0};
    int test_count = sizeof(test_powers) / sizeof(test_powers[0]);
    
    for (int i = 0; i < test_count; i++) {
        Serial.printf("[PLL_TEST] 测试功率: %d%%\n", test_powers[i]);
        setDigitalPLLPower(test_powers[i]);
        
        // 等待系统稳定
        delay(3000);
        
        // 检查状态
        bool stable = isDigitalPLLStable();
        int actual_power = getDigitalPLLPower();
        
        Serial.printf("[PLL_TEST] 结果 - 设置:%d%% 实际:%d%% 稳定:%s\n", 
                     test_powers[i], actual_power, stable ? "是" : "否");
        
        if (!stable) {
            Serial.printf("[PLL_TEST] 警告：功率 %d%% 时系统不稳定\n", test_powers[i]);
        }
    }
    
    Serial.println("[PLL_TEST] 测试序列完成");
    printDigitalPLLStatus();
}

/**
 * 在主程序的 setup() 函数中调用
 */
void setupDigitalPLL() {
    Serial.println("正在初始化数字锁相环系统...");
    
    if (initializeDigitalPLL(5)) { // 使用PWM通道5
        Serial.println("数字锁相环系统初始化成功");
        
        // 可选：运行初始化测试
        delay(1000);
        Serial.println("运行初始化测试...");
        setDigitalPLLPower(0);  // 确保从0功率开始
        
    } else {
        Serial.println("数字锁相环系统初始化失败，使用传统控制");
    }
}

/**
 * 在主程序的 loop() 函数中调用
 * 替换原有的 outPWM_update() 调用
 */
void loopDigitalPLL() {
    // 更新数字锁相环系统
    updateDigitalPLL();
    
    // 可选：定期状态检查
    static unsigned long last_status_check = 0;
    if (millis() - last_status_check > 30000) { // 每30秒检查一次
        last_status_check = millis();
        
        if (!isDigitalPLLStable()) {
            Serial.println("[PLL_LOOP] 警告：系统不稳定，检查状态");
            // 可以在这里添加自动恢复逻辑
        }
    }
}

/**
 * 清理资源
 */
void cleanupDigitalPLL() {
    if (g_pll_system) {
        delete g_pll_system;
        g_pll_system = nullptr;
    }
    
    if (g_protection_system) {
        delete g_protection_system;
        g_protection_system = nullptr;
    }
    
    Serial.println("[PLL_CLEANUP] 数字锁相环系统资源已清理");
}

/**
 * 集成到现有PID控制的示例
 * 在您的PID控制代码中替换功率输出部分
 */
void integrateWithExistingPID() {
    // 假设这是您现有的PID控制代码
    /*
    if (myPID.GetMode() == AUTOMATIC) {
        Input = beanTemperature;
        myPID.Compute();
        Output = constrain(Output, 0, 100);
        
        // 原来的代码：
        // heaterPowerLevel = Output;
        // outPWM_update();
        
        // 新的代码：使用数字锁相环
        handlePIDOutput(Output);
    }
    */
}

// 使用示例：
/*
在您的主程序中：

1. 在 setup() 函数中添加：
   setupDigitalPLL();

2. 在 loop() 函数中替换 outPWM_update() 为：
   loopDigitalPLL();

3. 在 check_Serial() 函数中添加：
   if (msg.startsWith("DPLL_")) {
       handleDigitalPLLCommands(msg);
       return;
   }

4. 在PID控制部分替换功率输出：
   // 原来：heaterPowerLevel = Output; outPWM_update();
   // 现在：handlePIDOutput(Output);

5. 在程序结束时（如果需要）：
   cleanupDigitalPLL();
*/
