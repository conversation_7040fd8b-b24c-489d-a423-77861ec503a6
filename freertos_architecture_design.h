// freertos_architecture_design.h
// FreeRTOS咖啡烘焙机控制系统架构设计
#ifndef FREERTOS_ARCHITECTURE_DESIGN_H
#define FREERTOS_ARCHITECTURE_DESIGN_H

#include <Arduino.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/queue.h>
#include <freertos/semphr.h>
#include <freertos/timers.h>
#include <freertos/event_groups.h>

// ============================================================================
// 系统架构配置
// ============================================================================

// 任务优先级定义（0-5，数字越大优先级越高）
#define PRIORITY_SAFETY_MONITOR     5    // 安全监控（最高优先级）
#define PRIORITY_PWM_CONTROL        4    // PWM实时控制
#define PRIORITY_PID_CONTROLLER     3    // PID控制算法
#define PRIORITY_PHASE_CONTROL      3    // 相位控制
#define PRIORITY_SENSOR_MANAGER     2    // 传感器管理
#define PRIORITY_FILTER_PROCESS     2    // 滤波处理
#define PRIORITY_SERIAL_COMM        1    // 串口通信
#define PRIORITY_BLE_COMM           1    // BLE通信
#define PRIORITY_DISPLAY_MGR        1    // 显示管理
#define PRIORITY_DATA_RECORD        0    // 数据记录
#define PRIORITY_FILE_MANAGER       0    // 文件管理

// 任务栈大小定义（字节）
#define STACK_SIZE_SAFETY_MONITOR   2048
#define STACK_SIZE_PWM_CONTROL      2048
#define STACK_SIZE_PID_CONTROLLER   3072
#define STACK_SIZE_PHASE_CONTROL    2048
#define STACK_SIZE_SENSOR_MANAGER   2048
#define STACK_SIZE_FILTER_PROCESS   2048
#define STACK_SIZE_SERIAL_COMM      3072
#define STACK_SIZE_BLE_COMM         4096
#define STACK_SIZE_DISPLAY_MGR      2048
#define STACK_SIZE_DATA_RECORD      3072
#define STACK_SIZE_FILE_MANAGER     4096

// 任务执行周期定义（毫秒）
#define CYCLE_SAFETY_MONITOR        20   // 50Hz安全监控
#define CYCLE_PWM_CONTROL           10   // 100Hz PWM控制
#define CYCLE_PID_CONTROLLER        100  // 10Hz PID控制
#define CYCLE_PHASE_CONTROL         50   // 20Hz相位控制
#define CYCLE_SENSOR_MANAGER        200  // 5Hz传感器采集
#define CYCLE_FILTER_PROCESS        100  // 10Hz滤波处理
#define CYCLE_DISPLAY_MGR           1000 // 1Hz显示更新
#define CYCLE_DATA_RECORD           1000 // 1Hz数据记录

// 队列大小定义
#define QUEUE_SIZE_SENSOR_DATA      10   // 传感器数据队列
#define QUEUE_SIZE_CONTROL_CMD      5    // 控制命令队列
#define QUEUE_SIZE_SERIAL_MSG       10   // 串口消息队列
#define QUEUE_SIZE_BLE_MSG          5    // BLE消息队列
#define QUEUE_SIZE_DISPLAY_DATA     3    // 显示数据队列
#define QUEUE_SIZE_LOG_DATA         20   // 日志数据队列

// ============================================================================
// 数据结构定义
// ============================================================================

// 传感器数据结构
typedef struct {
    float bean_temperature;      // 豆温
    float env_temperature;       // 环温
    float pot_value;            // 电位器值
    uint32_t timestamp;         // 时间戳
    bool valid;                 // 数据有效性
} SensorData_t;

// 控制命令结构
typedef struct {
    enum {
        CMD_SET_POWER,          // 设置功率
        CMD_SET_PID_PARAMS,     // 设置PID参数
        CMD_START_ROAST,        // 开始烘焙
        CMD_STOP_ROAST,         // 停止烘焙
        CMD_EMERGENCY_STOP      // 紧急停止
    } command_type;
    
    union {
        struct {
            float power_percent;
        } set_power;
        
        struct {
            float kp, ki, kd;
        } set_pid;
    } params;
    
    uint32_t timestamp;
} ControlCommand_t;

// PWM控制数据结构
typedef struct {
    float target_power;         // 目标功率
    float actual_power;         // 实际功率
    uint16_t pwm_value;         // PWM值
    bool safety_override;       // 安全覆盖
    uint32_t timestamp;
} PWMControlData_t;

// PID控制数据结构
typedef struct {
    float setpoint;             // 设定值
    float input;                // 输入值
    float output;               // 输出值
    float kp, ki, kd;          // PID参数
    bool auto_mode;             // 自动模式
    uint32_t timestamp;
} PIDControlData_t;

// 显示数据结构
typedef struct {
    float bean_temp;            // 豆温
    float env_temp;             // 环温
    float power_percent;        // 功率百分比
    uint32_t roast_time;        // 烘焙时间
    enum {
        STATUS_IDLE,
        STATUS_PREHEATING,
        STATUS_ROASTING,
        STATUS_COOLING,
        STATUS_ERROR
    } status;
    char message[64];           // 状态消息
} DisplayData_t;

// 日志数据结构
typedef struct {
    uint32_t timestamp;         // 时间戳
    float bean_temp;            // 豆温
    float env_temp;             // 环温
    float power_percent;        // 功率
    float pid_output;           // PID输出
    uint16_t ror;              // 升温速率
} LogData_t;

// ============================================================================
// 全局资源定义
// ============================================================================

// 任务句柄
extern TaskHandle_t xTaskSafetyMonitor;
extern TaskHandle_t xTaskPWMControl;
extern TaskHandle_t xTaskPIDController;
extern TaskHandle_t xTaskPhaseControl;
extern TaskHandle_t xTaskSensorManager;
extern TaskHandle_t xTaskFilterProcess;
extern TaskHandle_t xTaskSerialComm;
extern TaskHandle_t xTaskBLEComm;
extern TaskHandle_t xTaskDisplayMgr;
extern TaskHandle_t xTaskDataRecord;
extern TaskHandle_t xTaskFileManager;

// 队列句柄
extern QueueHandle_t xQueueSensorData;
extern QueueHandle_t xQueueControlCmd;
extern QueueHandle_t xQueueSerialMsg;
extern QueueHandle_t xQueueBLEMsg;
extern QueueHandle_t xQueueDisplayData;
extern QueueHandle_t xQueueLogData;

// 信号量句柄
extern SemaphoreHandle_t xMutexSPI;          // SPI总线互斥
extern SemaphoreHandle_t xMutexI2C;          // I2C总线互斥
extern SemaphoreHandle_t xMutexSerial;       // 串口互斥
extern SemaphoreHandle_t xMutexSharedData;   // 共享数据互斥

// 事件组句柄
extern EventGroupHandle_t xEventGroupSystem; // 系统事件组

// 系统事件位定义
#define EVENT_BIT_SYSTEM_READY      (1 << 0)  // 系统就绪
#define EVENT_BIT_SENSORS_READY     (1 << 1)  // 传感器就绪
#define EVENT_BIT_EMERGENCY_STOP    (1 << 2)  // 紧急停止
#define EVENT_BIT_ROAST_ACTIVE      (1 << 3)  // 烘焙激活
#define EVENT_BIT_DATA_LOGGING      (1 << 4)  // 数据记录
#define EVENT_BIT_BLE_CONNECTED     (1 << 5)  // BLE连接
#define EVENT_BIT_SD_CARD_READY     (1 << 6)  // SD卡就绪

// 软件定时器句柄
extern TimerHandle_t xTimerWatchdog;         // 看门狗定时器
extern TimerHandle_t xTimerHeartbeat;        // 心跳定时器

// ============================================================================
// 共享数据结构
// ============================================================================

// 系统状态结构（需要互斥保护）
typedef struct {
    // 当前状态
    SensorData_t current_sensor_data;
    PWMControlData_t current_pwm_data;
    PIDControlData_t current_pid_data;
    
    // 系统配置
    bool system_enabled;
    bool safety_mode;
    bool auto_mode;
    
    // 统计信息
    uint32_t system_uptime;
    uint32_t total_roast_time;
    uint16_t roast_count;
    
    // 错误状态
    uint32_t error_flags;
    char last_error[64];
} SystemState_t;

extern SystemState_t g_system_state;

// ============================================================================
// 核心API声明
// ============================================================================

// 系统初始化
bool FreeRTOS_System_Init(void);
void FreeRTOS_Create_Tasks(void);
void FreeRTOS_Create_Queues(void);
void FreeRTOS_Create_Semaphores(void);
void FreeRTOS_Create_EventGroups(void);
void FreeRTOS_Create_Timers(void);

// 任务函数声明
void Task_Safety_Monitor(void *pvParameters);
void Task_PWM_Control(void *pvParameters);
void Task_PID_Controller(void *pvParameters);
void Task_Phase_Control(void *pvParameters);
void Task_Sensor_Manager(void *pvParameters);
void Task_Filter_Process(void *pvParameters);
void Task_Serial_Comm(void *pvParameters);
void Task_BLE_Comm(void *pvParameters);
void Task_Display_Mgr(void *pvParameters);
void Task_Data_Record(void *pvParameters);
void Task_File_Manager(void *pvParameters);

// 定时器回调函数
void Timer_Watchdog_Callback(TimerHandle_t xTimer);
void Timer_Heartbeat_Callback(TimerHandle_t xTimer);

// 工具函数
bool System_Send_Control_Command(ControlCommand_t *cmd);
bool System_Get_Sensor_Data(SensorData_t *data, TickType_t timeout);
bool System_Set_PWM_Power(float power_percent);
bool System_Emergency_Stop(void);
void System_Print_Task_Stats(void);
void System_Print_Memory_Stats(void);

#endif // FREERTOS_ARCHITECTURE_DESIGN_H
