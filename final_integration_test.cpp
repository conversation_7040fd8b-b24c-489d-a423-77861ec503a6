// final_integration_test.cpp
// 最终集成测试 - 验证1600W发热丝和数字锁相环系统
#include <Arduino.h>

// 核心组件
#include "optimized_pwm_control.h"

// 可选组件（如果需要数字锁相环）
// #include "digital_pll_integration.h"

// 全局控制器
OptimizedPWMControl heaterController(5);  // PWM通道5

// 系统状态
bool system_initialized = false;
unsigned long last_status_print = 0;

void setup() {
    Serial.begin(115200);
    delay(1000);
    
    Serial.println("========================================");
    Serial.println("咖啡烘焙机 1600W发热丝系统启动");
    Serial.println("========================================");
    
    // 初始化1600W发热丝控制
    Serial.println("正在初始化1600W发热丝控制系统...");
    
    // 配置发热丝参数
    heaterController.setHeaterWattage(1600.0f);
    heaterController.setHeaterType(1600);
    
    // 启用安全保护
    heaterController.setSafetyMode(true);
    heaterController.setMaxSafePowerRatio(0.85f);  // 最大85%功率
    
    // 优化控制参数
    heaterController.setSmoothingFactor(0.12f);
    heaterController.setPowerChangeLimit(3);
    
    // 初始化PWM控制
    heaterController.begin();
    
    // 打印配置信息
    heaterController.printHeaterConfig();
    
    system_initialized = true;
    Serial.println("✅ 1600W发热丝系统初始化完成");
    Serial.println("========================================");
    
    // 可选：初始化数字锁相环
    /*
    if (GlobalPLLInterface::initialize(5)) {
        Serial.println("✅ 数字锁相环系统初始化完成");
    } else {
        Serial.println("⚠️  数字锁相环初始化失败，使用传统控制");
    }
    */
    
    Serial.println("系统准备就绪，可以开始使用");
    printHelp();
}

void loop() {
    if (!system_initialized) return;
    
    // 更新发热丝控制
    heaterController.update();
    
    // 可选：更新数字锁相环
    // GlobalPLLInterface::updatePLL();
    
    // 处理串口命令
    handleSerialCommands();
    
    // 定期状态报告
    if (millis() - last_status_print > 30000) {  // 每30秒
        last_status_print = millis();
        printSystemStatus();
    }
    
    delay(10);  // 小延迟避免过度占用CPU
}

void handleSerialCommands() {
    if (!Serial.available()) return;
    
    String command = Serial.readString();
    command.trim();
    command.toUpperCase();
    
    if (command.startsWith("POWER,")) {
        // 设置功率：POWER,25
        int power = command.substring(6).toInt();
        heaterController.setPower(power);
        Serial.printf("✅ 发热丝功率设置为: %d%%\n", power);
        
    } else if (command == "STATUS") {
        // 显示系统状态
        printSystemStatus();
        
    } else if (command == "CONFIG") {
        // 显示配置信息
        heaterController.printHeaterConfig();
        
    } else if (command == "DEBUG") {
        // 显示调试信息
        heaterController.printDebugInfo();
        
    } else if (command == "TABLE") {
        // 显示功率线性化表
        heaterController.printLinearizationTable();
        
    } else if (command.startsWith("SAFETY,")) {
        // 安全模式：SAFETY,1 或 SAFETY,0
        bool enable = command.substring(7).toInt() != 0;
        heaterController.setSafetyMode(enable);
        Serial.printf("✅ 安全模式: %s\n", enable ? "启用" : "禁用");
        
    } else if (command.startsWith("LIMIT,")) {
        // 功率限制：LIMIT,80
        float limit = command.substring(6).toFloat() / 100.0f;
        heaterController.setMaxSafePowerRatio(limit);
        Serial.printf("✅ 最大功率限制: %.1f%%\n", limit * 100);
        
    } else if (command == "TEST") {
        // 运行测试序列
        runHeaterTest();
        
    } else if (command == "RESET") {
        // 重置系统
        heaterController.reset();
        Serial.println("✅ 系统已重置");
        
    } else if (command == "HELP") {
        // 显示帮助
        printHelp();
        
    } else if (command.length() > 0) {
        Serial.println("❌ 未知命令，输入 HELP 查看可用命令");
    }
}

void printSystemStatus() {
    Serial.println("========== 系统状态 ==========");
    Serial.printf("发热丝功率: %.0fW\n", heaterController.getHeaterWattage());
    Serial.printf("当前设置: %d%%\n", heaterController.getCurrentPower());
    Serial.printf("安全模式: %s\n", heaterController.isSafetyModeEnabled() ? "启用" : "禁用");
    
    if (heaterController.isSafetyModeEnabled()) {
        Serial.printf("功率限制: %.1f%%\n", heaterController.getMaxSafePowerRatio() * 100);
    }
    
    Serial.printf("系统运行时间: %lu 秒\n", millis() / 1000);
    Serial.println("=============================");
}

void runHeaterTest() {
    Serial.println("🧪 开始发热丝测试序列...");
    
    int test_powers[] = {0, 10, 15, 20, 25, 30, 20, 10, 0};
    int test_count = sizeof(test_powers) / sizeof(test_powers[0]);
    
    for (int i = 0; i < test_count; i++) {
        Serial.printf("📊 测试功率: %d%%\n", test_powers[i]);
        heaterController.setPower(test_powers[i]);
        
        // 等待稳定
        delay(2000);
        
        // 计算实际功率
        float linear_coeff = heaterController.linearizePWMPower(test_powers[i]);
        float actual_watts = heaterController.getHeaterWattage() * linear_coeff;
        
        Serial.printf("   设置: %d%% -> 实际: %.0fW (%.1f%%)\n", 
                     test_powers[i], actual_watts, linear_coeff * 100);
        
        // 检查安全限制
        if (heaterController.isSafetyModeEnabled() && 
            test_powers[i] > heaterController.getMaxSafePowerRatio() * 100) {
            Serial.println("   ⚠️  功率被安全系统限制");
        }
    }
    
    Serial.println("✅ 测试序列完成");
    heaterController.printDebugInfo();
}

void printHelp() {
    Serial.println("========== 可用命令 ==========");
    Serial.println("POWER,<0-100>    设置功率百分比");
    Serial.println("STATUS           显示系统状态");
    Serial.println("CONFIG           显示配置信息");
    Serial.println("DEBUG            显示调试信息");
    Serial.println("TABLE            显示功率线性化表");
    Serial.println("SAFETY,<0|1>     启用/禁用安全模式");
    Serial.println("LIMIT,<50-100>   设置最大功率限制");
    Serial.println("TEST             运行测试序列");
    Serial.println("RESET            重置系统");
    Serial.println("HELP             显示此帮助");
    Serial.println("=============================");
}

// PID集成示例函数
void integratePIDControl(float pid_output) {
    // 将PID输出转换为功率百分比
    int power_percent = (int)constrain(pid_output, 0, 100);
    
    // 设置发热丝功率
    heaterController.setPower(power_percent);
    
    // 可选：记录控制状态
    static unsigned long last_log = 0;
    if (millis() - last_log > 5000) {  // 每5秒记录一次
        last_log = millis();
        float linear_coeff = heaterController.linearizePWMPower(power_percent);
        float actual_watts = heaterController.getHeaterWattage() * linear_coeff;
        
        Serial.printf("[PID] 输出:%.1f%% -> 功率:%d%% -> 实际:%.0fW\n", 
                     pid_output, power_percent, actual_watts);
    }
}

/*
使用说明：

1. 编译并上传此程序到ESP32
2. 打开串口监视器（115200波特率）
3. 输入命令测试系统功能

基础测试：
- POWER,15    // 设置15%功率
- STATUS      // 查看状态
- TEST        // 运行完整测试

安全测试：
- SAFETY,1    // 启用安全模式
- LIMIT,80    // 设置80%功率限制
- POWER,90    // 尝试设置90%（应被限制）

在您的PID控制代码中：
- 用 integratePIDControl(pid_output) 替换原有的功率设置
- 用 heaterController.update() 替换 outPWM_update()
*/
