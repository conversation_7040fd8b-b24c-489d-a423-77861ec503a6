// freertos_system_init.cpp
// FreeRTOS系统初始化实现
#include "freertos_architecture_design.h"

// ============================================================================
// 全局变量定义
// ============================================================================

// 任务句柄
TaskHandle_t xTaskSafetyMonitor = NULL;
TaskHandle_t xTaskPWMControl = NULL;
TaskHandle_t xTaskPIDController = NULL;
TaskHandle_t xTaskPhaseControl = NULL;
TaskHandle_t xTaskSensorManager = NULL;
TaskHandle_t xTaskFilterProcess = NULL;
TaskHandle_t xTaskSerialComm = NULL;
TaskHandle_t xTaskBLEComm = NULL;
TaskHandle_t xTaskDisplayMgr = NULL;
TaskHandle_t xTaskDataRecord = NULL;
TaskHandle_t xTaskFileManager = NULL;

// 队列句柄
QueueHandle_t xQueueSensorData = NULL;
QueueHandle_t xQueueControlCmd = NULL;
QueueHandle_t xQueueSerialMsg = NULL;
QueueHandle_t xQueueBLEMsg = NULL;
QueueHandle_t xQueueDisplayData = NULL;
QueueHandle_t xQueueLogData = NULL;

// 信号量句柄
SemaphoreHandle_t xMutexSPI = NULL;
SemaphoreHandle_t xMutexI2C = NULL;
SemaphoreHandle_t xMutexSerial = NULL;
SemaphoreHandle_t xMutexSharedData = NULL;

// 事件组句柄
EventGroupHandle_t xEventGroupSystem = NULL;

// 定时器句柄
TimerHandle_t xTimerWatchdog = NULL;
TimerHandle_t xTimerHeartbeat = NULL;

// 系统状态
SystemState_t g_system_state;

// ============================================================================
// 系统初始化函数
// ============================================================================

bool FreeRTOS_System_Init(void) {
    Serial.println("========================================");
    Serial.println("FreeRTOS咖啡烘焙机控制系统初始化");
    Serial.println("========================================");
    
    // 1. 初始化系统状态
    memset(&g_system_state, 0, sizeof(SystemState_t));
    g_system_state.system_enabled = true;
    g_system_state.safety_mode = true;
    
    // 2. 创建同步对象
    if (!FreeRTOS_Create_Semaphores()) {
        Serial.println("❌ 信号量创建失败");
        return false;
    }
    
    if (!FreeRTOS_Create_EventGroups()) {
        Serial.println("❌ 事件组创建失败");
        return false;
    }
    
    // 3. 创建队列
    if (!FreeRTOS_Create_Queues()) {
        Serial.println("❌ 队列创建失败");
        return false;
    }
    
    // 4. 创建定时器
    if (!FreeRTOS_Create_Timers()) {
        Serial.println("❌ 定时器创建失败");
        return false;
    }
    
    // 5. 创建任务
    if (!FreeRTOS_Create_Tasks()) {
        Serial.println("❌ 任务创建失败");
        return false;
    }
    
    Serial.println("✅ FreeRTOS系统初始化完成");
    Serial.printf("✅ 可用堆内存: %d bytes\n", esp_get_free_heap_size());
    Serial.printf("✅ 最小可用堆内存: %d bytes\n", esp_get_minimum_free_heap_size());
    
    return true;
}

void FreeRTOS_Create_Semaphores(void) {
    Serial.println("创建信号量...");
    
    // 创建互斥锁
    xMutexSPI = xSemaphoreCreateMutex();
    xMutexI2C = xSemaphoreCreateMutex();
    xMutexSerial = xSemaphoreCreateMutex();
    xMutexSharedData = xSemaphoreCreateMutex();
    
    if (xMutexSPI && xMutexI2C && xMutexSerial && xMutexSharedData) {
        Serial.println("✅ 所有互斥锁创建成功");
        return true;
    } else {
        Serial.println("❌ 互斥锁创建失败");
        return false;
    }
}

bool FreeRTOS_Create_EventGroups(void) {
    Serial.println("创建事件组...");
    
    xEventGroupSystem = xEventGroupCreate();
    
    if (xEventGroupSystem) {
        // 设置初始事件位
        xEventGroupSetBits(xEventGroupSystem, EVENT_BIT_SYSTEM_READY);
        Serial.println("✅ 系统事件组创建成功");
        return true;
    } else {
        Serial.println("❌ 事件组创建失败");
        return false;
    }
}

bool FreeRTOS_Create_Queues(void) {
    Serial.println("创建消息队列...");
    
    // 创建各种队列
    xQueueSensorData = xQueueCreate(QUEUE_SIZE_SENSOR_DATA, sizeof(SensorData_t));
    xQueueControlCmd = xQueueCreate(QUEUE_SIZE_CONTROL_CMD, sizeof(ControlCommand_t));
    xQueueSerialMsg = xQueueCreate(QUEUE_SIZE_SERIAL_MSG, 256);  // 字符串消息
    xQueueBLEMsg = xQueueCreate(QUEUE_SIZE_BLE_MSG, 256);
    xQueueDisplayData = xQueueCreate(QUEUE_SIZE_DISPLAY_DATA, sizeof(DisplayData_t));
    xQueueLogData = xQueueCreate(QUEUE_SIZE_LOG_DATA, sizeof(LogData_t));
    
    if (xQueueSensorData && xQueueControlCmd && xQueueSerialMsg && 
        xQueueBLEMsg && xQueueDisplayData && xQueueLogData) {
        Serial.println("✅ 所有队列创建成功");
        return true;
    } else {
        Serial.println("❌ 队列创建失败");
        return false;
    }
}

bool FreeRTOS_Create_Timers(void) {
    Serial.println("创建软件定时器...");
    
    // 创建看门狗定时器（5秒超时）
    xTimerWatchdog = xTimerCreate(
        "Watchdog",                    // 定时器名称
        pdMS_TO_TICKS(5000),          // 周期5秒
        pdFALSE,                      // 单次触发
        (void *)0,                    // 定时器ID
        Timer_Watchdog_Callback       // 回调函数
    );
    
    // 创建心跳定时器（1秒周期）
    xTimerHeartbeat = xTimerCreate(
        "Heartbeat",                  // 定时器名称
        pdMS_TO_TICKS(1000),         // 周期1秒
        pdTRUE,                      // 自动重载
        (void *)1,                   // 定时器ID
        Timer_Heartbeat_Callback     // 回调函数
    );
    
    if (xTimerWatchdog && xTimerHeartbeat) {
        // 启动心跳定时器
        xTimerStart(xTimerHeartbeat, 0);
        Serial.println("✅ 定时器创建成功");
        return true;
    } else {
        Serial.println("❌ 定时器创建失败");
        return false;
    }
}

bool FreeRTOS_Create_Tasks(void) {
    Serial.println("创建系统任务...");
    
    BaseType_t xReturned;
    
    // 创建安全监控任务（最高优先级）
    xReturned = xTaskCreatePinnedToCore(
        Task_Safety_Monitor,           // 任务函数
        "SafetyMonitor",              // 任务名称
        STACK_SIZE_SAFETY_MONITOR,    // 栈大小
        NULL,                         // 参数
        PRIORITY_SAFETY_MONITOR,      // 优先级
        &xTaskSafetyMonitor,         // 任务句柄
        0                            // 核心0
    );
    if (xReturned != pdPASS) {
        Serial.println("❌ 安全监控任务创建失败");
        return false;
    }
    
    // 创建PWM控制任务
    xReturned = xTaskCreatePinnedToCore(
        Task_PWM_Control,
        "PWMControl",
        STACK_SIZE_PWM_CONTROL,
        NULL,
        PRIORITY_PWM_CONTROL,
        &xTaskPWMControl,
        0                            // 核心0（实时任务）
    );
    if (xReturned != pdPASS) {
        Serial.println("❌ PWM控制任务创建失败");
        return false;
    }
    
    // 创建PID控制任务
    xReturned = xTaskCreatePinnedToCore(
        Task_PID_Controller,
        "PIDController",
        STACK_SIZE_PID_CONTROLLER,
        NULL,
        PRIORITY_PID_CONTROLLER,
        &xTaskPIDController,
        0                            // 核心0
    );
    if (xReturned != pdPASS) {
        Serial.println("❌ PID控制任务创建失败");
        return false;
    }
    
    // 创建传感器管理任务
    xReturned = xTaskCreatePinnedToCore(
        Task_Sensor_Manager,
        "SensorManager",
        STACK_SIZE_SENSOR_MANAGER,
        NULL,
        PRIORITY_SENSOR_MANAGER,
        &xTaskSensorManager,
        1                            // 核心1
    );
    if (xReturned != pdPASS) {
        Serial.println("❌ 传感器管理任务创建失败");
        return false;
    }
    
    // 创建滤波处理任务
    xReturned = xTaskCreatePinnedToCore(
        Task_Filter_Process,
        "FilterProcess",
        STACK_SIZE_FILTER_PROCESS,
        NULL,
        PRIORITY_FILTER_PROCESS,
        &xTaskFilterProcess,
        1                            // 核心1
    );
    if (xReturned != pdPASS) {
        Serial.println("❌ 滤波处理任务创建失败");
        return false;
    }
    
    // 创建串口通信任务
    xReturned = xTaskCreatePinnedToCore(
        Task_Serial_Comm,
        "SerialComm",
        STACK_SIZE_SERIAL_COMM,
        NULL,
        PRIORITY_SERIAL_COMM,
        &xTaskSerialComm,
        1                            // 核心1
    );
    if (xReturned != pdPASS) {
        Serial.println("❌ 串口通信任务创建失败");
        return false;
    }
    
    // 创建显示管理任务
    xReturned = xTaskCreatePinnedToCore(
        Task_Display_Mgr,
        "DisplayMgr",
        STACK_SIZE_DISPLAY_MGR,
        NULL,
        PRIORITY_DISPLAY_MGR,
        &xTaskDisplayMgr,
        1                            // 核心1
    );
    if (xReturned != pdPASS) {
        Serial.println("❌ 显示管理任务创建失败");
        return false;
    }
    
    Serial.println("✅ 所有任务创建成功");
    Serial.println("任务分配：");
    Serial.println("  核心0: 安全监控、PWM控制、PID控制");
    Serial.println("  核心1: 传感器、滤波、通信、显示");
    
    return true;
}

// ============================================================================
// 定时器回调函数
// ============================================================================

void Timer_Watchdog_Callback(TimerHandle_t xTimer) {
    Serial.println("⚠️ 看门狗超时！系统可能存在问题");
    
    // 触发紧急停止
    xEventGroupSetBits(xEventGroupSystem, EVENT_BIT_EMERGENCY_STOP);
    
    // 记录错误
    strcpy(g_system_state.last_error, "Watchdog timeout");
    g_system_state.error_flags |= 0x01;
}

void Timer_Heartbeat_Callback(TimerHandle_t xTimer) {
    // 更新系统运行时间
    g_system_state.system_uptime = xTaskGetTickCount() / 1000;
    
    // 重置看门狗
    xTimerReset(xTimerWatchdog, 0);
    
    // 可选：LED心跳指示
    static bool led_state = false;
    led_state = !led_state;
    // digitalWrite(LED_PIN, led_state);
}

// ============================================================================
// 工具函数实现
// ============================================================================

bool System_Send_Control_Command(ControlCommand_t *cmd) {
    if (!cmd) return false;
    
    return xQueueSend(xQueueControlCmd, cmd, pdMS_TO_TICKS(100)) == pdTRUE;
}

bool System_Get_Sensor_Data(SensorData_t *data, TickType_t timeout) {
    if (!data) return false;
    
    if (xSemaphoreTake(xMutexSharedData, timeout) == pdTRUE) {
        *data = g_system_state.current_sensor_data;
        xSemaphoreGive(xMutexSharedData);
        return true;
    }
    
    return false;
}

bool System_Set_PWM_Power(float power_percent) {
    ControlCommand_t cmd;
    cmd.command_type = CMD_SET_POWER;
    cmd.params.set_power.power_percent = power_percent;
    cmd.timestamp = xTaskGetTickCount();
    
    return System_Send_Control_Command(&cmd);
}

bool System_Emergency_Stop(void) {
    // 设置紧急停止事件
    xEventGroupSetBits(xEventGroupSystem, EVENT_BIT_EMERGENCY_STOP);
    
    // 立即停止PWM输出
    System_Set_PWM_Power(0);
    
    Serial.println("🚨 紧急停止激活！");
    
    return true;
}

void System_Print_Task_Stats(void) {
    Serial.println("========== 任务状态统计 ==========");
    
    char *pcWriteBuffer = (char *)malloc(1024);
    if (pcWriteBuffer) {
        vTaskList(pcWriteBuffer);
        Serial.println("任务名称\t状态\t优先级\t栈剩余\t任务号");
        Serial.println(pcWriteBuffer);
        free(pcWriteBuffer);
    }
    
    Serial.println("================================");
}

void System_Print_Memory_Stats(void) {
    Serial.println("========== 内存使用统计 ==========");
    Serial.printf("当前可用堆内存: %d bytes\n", esp_get_free_heap_size());
    Serial.printf("最小可用堆内存: %d bytes\n", esp_get_minimum_free_heap_size());
    Serial.printf("最大可分配块: %d bytes\n", heap_caps_get_largest_free_block(MALLOC_CAP_8BIT));
    Serial.println("================================");
}
