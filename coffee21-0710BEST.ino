#include <arduino.h>
#include "user_config.h"      // 引入用户配置文件
#include <PID_v1.h>           // 导入PID库
// SPP蓝牙已禁用
// #include <BluetoothSerial.h>  // 导入蓝牙串口库
#include <BLEDevice.h>
#include <BLEServer.h>
#include <BLEUtils.h>
#include <BLE2902.h>
#include <driver/ledc.h>      // 导入LEDC库
#include <math.h>

#include "display.h"       // 导入显示库
#include "get_pot.h"       // 导入pot库
#include "get_temp.h"      // 导入温度库
#include "file_handler.h"  // 引入文件操作库

// 优化的PWM控制 - 专为无过零检测设计
#ifdef USE_OPTIMIZED_PWM_CONTROL
#include "optimized_pwm_control.h"
#endif

// 为 file_handler.h 中声明的外部变量提供定义
curve_t DataCurve[600];
int curveIndex = 0;
String DataDescription;

// 存储BT历史数据用于ROR计算
static std::vector<float> bt_history;

// 温度相关常量定义
const int CURVE_RECORD_BUFFER_SIZE = 600;
const unsigned long BLUETOOTH_DELAY_MS = 20;
const unsigned long CURVE_AUTO_SAVE_TIME_SECONDS = 120;

// 温度设置
float ambientTemperature = 20.00;     // 环境温度 (原AT)
float exhaustTemperature = 0;         // 风温 (原ET)  
float beanTemperature = 0;            // 豆温 (原BT)
float rateOfRise = 0;                 // 每分钟升温速率 (原ROR)
double targetTemperature = 0;         // 目标温度 (原Setpoint)

//  输出控制变量
int heaterPowerLevel = 0;                 // 功率大小 0-100 (原levelFIR)
int fanSpeedLevel = 0;                    // 风力大小 0-100 (原levelFAN)
int whiteLedBrightness = 0;               // 白光亮度 (原level_LED_WHTE)
int yellowLedBrightness = 0;              // LED黄光亮度 (原level_LED_YELLOW)
bool isCommandControlActive = false;      // 命令控制激活标志 (原cmd_control_active)

int previousHeaterPowerLevel = 0;         // 原prev_levelFIR
int previousFanSpeedLevel = 0;            // 原prev_levelFAN

unsigned long heaterStartTime = 0;       // 原fir_start_time
unsigned long roastingDurationSeconds;   // 开火时长度（单位秒）(原Sfir_time)
bool isRoastingStarted = false;          // 烘培开始标志 (原start_flag)

// 显示和保存相关标志
bool isDisplayUpdateRequired = false;     // 显示刷新脉冲 (原lcd_flag)
bool shouldPromptCurveSave = false;       // 是否提示自动保存曲线 (原save_flag)

// 曲线数组,这里定义了600个记录点，假如每秒记录一个可记录10分钟，
// 如果风力火力每10秒改变一次可记录100分钟，可视内存使用情况增加或减少
curve_t recordCurve[CURVE_RECORD_BUFFER_SIZE];   // 记录曲线数据
int recordCurveIndex = 0;  // 记录曲线数据点索引

machine_state operatingMode = SHOUDONG;  // 原mode

curve_t runCurve[CURVE_RECORD_BUFFER_SIZE];      // 运行曲线数据
int runCurvePoint = 0;     //运行数组点数量
int currentCurveId = DEFAULT_CURVE_ID;  // 当前曲线ID，初始化为默认值

unsigned long roastingTime = 0;           // 原fir_time
String roastDescription = "";             // 原description
unsigned long lastCurveRecordTime = 0;    // 上次记录曲线数据的时间戳 (原last_curve_record_time)

// 温度单位
bool useCelsiusScale = true;  // 使用摄氏度(true),还是使用华氏度(false) (原Cscale)

// SPP蓝牙已禁用
// BluetoothSerial SerialBT;  // 声明蓝牙串口对象

bool isBluetoothAuthenticated;            // 原authenticated
bool isBluetoothModeEnabled = true;       // BLE蓝牙模式 (原BT_MODE)
BLECharacteristic *bleCharacteristic;
BLECharacteristic *pNotifyCharacteristic;
BLEServer *pBLEServer = NULL;  // 全局BLE服务器对象
// 触摸屏相关变量

#define SERVICE_UUID        "6e400001-b5a3-f393-e0a9-e50e24dcca9e"
#define CHARACTERISTIC_UUID "6e400002-b5a3-f393-e0a9-e50e24dcca9e"
#define NOTIFY_CHARACTERISTIC_UUID "6e400003-b5a3-f393-e0a9-e50e24dcca9e"

// PID控制相关变量
double ct = CT;
double Kp = PRO;
double Ki = INT;
double Kd = DER;
double Input = 0;
double Output = 0;

// 创建 PID 实例
PID myPID(&Input, &Output, &targetTemperature, Kp, Ki, Kd, DIRECT);
// 创建6675对象
MAX6675 thermocouple(thermoCLK, thermoCS1, thermoDO);
MAX6675 thermocouple2(thermoCLK, thermoCS2, thermoDO);

// 优化的PWM控制对象
#ifdef USE_OPTIMIZED_PWM_CONTROL
OptimizedPWMControl pwmController(FIR_PWM_CHAN);
OptimizedPowerStabilityMonitor stabilityMonitor;
#endif
// 创建MLX90614对象
Adafruit_MLX90614 mlx = Adafruit_MLX90614();
//-----------------------------------------------------------------
// 发送数据到上位机-------------------------------------------------

void logger() {
  // 添加调试信息，显示当前温度值
  // Serial.println("[DEBUG] Current temperatures - AT: " + String(AT) + ", ET: " + String(ET) + ", BT: " + String(BT));
  
  String log = "";
  log += ambientTemperature;  // 发送环境温度
  log += ",";
  log += exhaustTemperature;  // 通道1
  log += ",";
  log += beanTemperature;  // 通道2
  log += ",";
  log += "53";  //  通道3
  log += ",";
  log += "54";  //  通道4
  log += ",";
  log += String(heaterPowerLevel);  // 发送加热器当前功率
  log += ",";
  log += String(fanSpeedLevel);
  if (myPID.GetMode() != MANUAL) {  // 如果PID控制器处于自动模式，发送PID输出值
    log += ",";
    log += String(targetTemperature);
  } else {
    log += ",";
    log += "0";  // 发送PID输出值为0
  }
  log += "\n";  // 发送数据结束
  
  // Serial.println("[DEBUG] Formatted log string: " + log.substring(0, log.length()-1)); // 去掉换行符显示
  // 数据整理完毕，准备发送
  if (isBluetoothModeEnabled) {
#ifdef USE_BLE
    // BLE 通信 - 检查设备是否连接
    if (bleCharacteristic && pBLEServer && pBLEServer->getConnectedCount() > 0) {
      // Serial.println("[BLE] Sending main data: " + log);
      bleCharacteristic->setValue(log.c_str());
      bleCharacteristic->notify();
      delay(20); // 增加延迟确保数据发送完成
    }
    // 通过第二个特征值发送完整数据副本，确保上位机能接收到BT和ET
    if (pNotifyCharacteristic && pBLEServer && pBLEServer->getConnectedCount() > 0) {
      // Serial.println("[BLE] Sending notify data: " + log);
      pNotifyCharacteristic->setValue(log.c_str());
      pNotifyCharacteristic->notify();
      delay(20); // 增加延迟确保数据发送完成
    } else if (pBLEServer && pBLEServer->getConnectedCount() == 0) {
      Serial.println("[BLE] No client connected, skipping data send");
    }
#endif
  } 
  // SPP蓝牙已禁用
  // else {
  //   // SPP 通信
  //   SerialBT.print(log);
  // }
  Serial.print(log);  // 调试用
}
//-----------------------------------------------------------------

void handleOT1Command(String msg) {
  String stmp = msg.substring(4);  // 去除"OT1,"取后面的数字
  if (fanSpeedLevel > HTR_CUTOFF_FAN_VAL) {
    heaterPowerLevel = stmp.toInt();
  }
}

void handleOT3Command(String msg) {
  String stmp = msg.substring(6);  // 去除"DCFAN,"
  fanSpeedLevel = stmp.toInt();
  if (fanSpeedLevel <= HTR_CUTOFF_FAN_VAL) {
    heaterPowerLevel = 0;
  }
}

// 风扇测试命令处理
void handleFanTestCommand(String msg) {
  String stmp = msg.substring(8);  // 去除"FANTEST,"
  int testSpeed = stmp.toInt();
  if (testSpeed >= 0 && testSpeed <= 100) {
    fanSpeedLevel = testSpeed;
    outPWM_update(); // 立即应用PWM更新，会自动显示映射关系
    
    // 计算映射后的实际值
    int effective_speed = map(fanSpeedLevel, 0, 100, 0, FAN_HARDWARE_LIMIT);
    int pwm_main = map(effective_speed, 0, 100, 0, 4095);
    int pwm_servo = map(effective_speed, 0, 100, 0, SERVO_MAX);
    
    Serial.printf("[FAN_TEST] 智能映射测试 - 用户:%d%% -> 硬件:%d%%\n", 
                  testSpeed, effective_speed);
    Serial.printf("[FAN_TEST] PWM值:%d (%.1f%%) | 舵机角度:%d°\n", 
                  pwm_main, (pwm_main/4095.0)*100, pwm_servo);
  }
}

// 风扇PWM调试功能 - 直接设置FAN_PWM_CHAN值
void handlePWMDebugCommand(String msg) {
  String stmp = msg.substring(8);  // 去除"PWMDEBUG,"
  int pwmValue = stmp.toInt();
  
  if (pwmValue >= 0 && pwmValue <= 4095) {
    ledcWriteChannel(FAN_PWM_CHAN, pwmValue);  // 直接控制主要的风扇PWM通道
    Serial.printf("[PWM_DEBUG] FAN_PWM_CHAN通道PWM值：%d (%.1f%%)\n", pwmValue, (pwmValue / 4095.0) * 100);
    
    // 临时禁用正常风扇控制
    fanSpeedLevel = -1;  // 标记为调试模式
  } else {
    Serial.println("[PWM_DEBUG] 错误：PWM值必须在0-4095范围内");
  }
}

// 恢复正常风扇控制
void handleResetFanCommand(String msg) {
  fanSpeedLevel = 0;
  outPWM_update();  // 恢复正常的PWM输出
  Serial.println("[FAN_RESET] 已恢复正常风扇控制模式");
}

// PWM扫描测试 - 自动测试不同PWM值
void handlePulseScanCommand(String msg) {
  Serial.println("[PWM_SCAN] 开始PWM值扫描测试（FAN_PWM_CHAN通道）...");
  Serial.println("[PWM_SCAN] 格式：PWM值 | 百分比 | 风扇响应");
  
  // 从0到4095，每200测试一次
  for (int pwm = 0; pwm <= 4095; pwm += 200) {
    float percentage = (pwm / 4095.0) * 100;
    ledcWriteChannel(FAN_PWM_CHAN, pwm);
    Serial.printf("[PWM_SCAN] PWM:%4d | %5.1f%% | 请观察风扇\n", pwm, percentage);
    delay(1500);  // 每个PWM值测试1.5秒
  }
  
  // 回到零速
  fanSpeedLevel = 0;
  outPWM_update();
  Serial.println("[PWM_SCAN] PWM扫描完成，请记录风扇响应的PWM范围");
}

// 单通道测试 - 只使用一个PWM通道进行控制
void handleSingleChannelTestCommand(String msg) {
  String stmp = msg.substring(11);  // 去除"SINGLETEST,"
  int testSpeed = stmp.toInt();
  
  if (testSpeed >= 0 && testSpeed <= 100) {
    // 仅使用主PWM通道，禁用舵机控制
    int pwm_value = map(testSpeed, 0, 100, 0, 4095);
    ledcWriteChannel(FAN_PWM_CHAN, pwm_value);
    
    // 关闭舵机控制
    setServoAngle(SERVO_CHAN, 0);
    setServoAngle(SERVO2_CHAN, 0);
    
    Serial.printf("[SINGLE_TEST] 单通道测试 %d%% | PWM值:%d | 实际百分比:%.2f%%\n", 
                  testSpeed, pwm_value, (pwm_value/4095.0)*100);
    Serial.println("[SINGLE_TEST] 仅使用FAN_PWM_CHAN控制，舵机通道已禁用");
    
    // 标记为调试模式
    fanSpeedLevel = -1;
  } else {
    Serial.println("[SINGLE_TEST] 错误：速度值必须在0-100范围内");
  }
}

// 火力测试 - 验证PWM频率优化效果
void handleHeaterTestCommand(String msg) {
  String stmp = msg.substring(11);  // 去除"HEATERTEST,"
  int testPower = stmp.toInt();
  
  if (testPower >= 0 && testPower <= 100) {
    heaterPowerLevel = testPower;
    outPWM_update(); // 立即应用PWM更新
    
    int pwm_value = map(testPower, 0, 100, 0, 4095);
    
    Serial.printf("[HEATER_TEST] 火力测试 %d%% | PWM值:%d | PWM频率:%dHz\n", 
                  testPower, pwm_value, FIR_FREQ);
    Serial.printf("[HEATER_TEST] 优化效果：%dHz高频PWM消除脉冲开关效应\n", FIR_FREQ);
    Serial.println("[HEATER_TEST] 请观察温度曲线稳定性改善");
    
    // 如果火力>0，提醒观察温度
    if (testPower > 0) {
      Serial.println("[HEATER_TEST] 警告：火力已开启，请观察温度变化");
    }
  } else {
    Serial.println("[HEATER_TEST] 错误：火力值必须在0-100范围内");
  }
}

// 温度监控 - 持续显示温度数据用于验证稳定性
void handleTempMonitorCommand(String msg) {
  static bool monitoring = false;
  monitoring = !monitoring;
  
  if (monitoring) {
    Serial.println("[TEMP_MONITOR] 开始强化温度监控模式");
    Serial.println("[TEMP_MONITOR] 监控项目：");
    Serial.println("[TEMP_MONITOR] - PWM频率：20kHz高频控制");
    Serial.println("[TEMP_MONITOR] - 卡尔曼滤波：BT/ET专用滤波器");
    Serial.println("[TEMP_MONITOR] - 变化率限制：2℃/周期"); 
    Serial.println("[TEMP_MONITOR] - 采样间隔：1500ms深度平滑");
    Serial.printf("[TEMP_MONITOR] 当前配置：Q=%.2f R=%.2f PWM频率=%dHz\n", 
                  Q_kalman, R_kalman, FIR_FREQ);
  } else {
    Serial.println("[TEMP_MONITOR] 停止温度监控模式");
  }
  
  // 设置全局监控标志(需要在loop中实现)
  static bool tempMonitorActive = monitoring;
  tempMonitorActive = monitoring;
}

// 稳定性测试 - 综合验证所有优化效果
void handleStabilityTestCommand(String msg) {
  Serial.println("[STABILITY_TEST] ========== 温度稳定性综合测试 ==========");
  Serial.printf("[STABILITY_TEST] PWM频率优化：%dHz（消除脉冲控制）\n", FIR_FREQ);
  Serial.printf("[STABILITY_TEST] 卡尔曼滤波强度：Q=%.2f R=%.2f（强平滑）\n", Q_kalman, R_kalman);
  Serial.printf("[STABILITY_TEST] 采样间隔：%dms（深度稳定）\n", TEMP_UPDATE_INTERVAL);
  #ifdef ENABLE_TEMP_RATE_LIMIT
  Serial.printf("[STABILITY_TEST] 变化率限制：%.1f℃/周期（防跳变）\n", TEMP_CHANGE_LIMIT);
  #else
  Serial.println("[STABILITY_TEST] 变化率限制：已禁用");
  #endif
  Serial.println("[STABILITY_TEST] 卡尔曼滤波：BT/ET专用滤波器（超强平滑）");
  Serial.println("[STABILITY_TEST] =====================================");
  Serial.println("[STABILITY_TEST] 请观察温度曲线是否比优化前更稳定平滑");
  Serial.println("[STABILITY_TEST] 可以发送'HEATERTEST,50'测试火力稳定性");
}

// 温度单调性测试 - 验证温度单调性保证功能
void handleMonotonicTestCommand(String msg) {
  Serial.println("[MONOTONIC_TEST] ========== 温度单调性保证系统测试 ==========");
  
  #ifdef ENABLE_MONOTONIC_TEMPERATURE
  Serial.println("[MONOTONIC_TEST] 单调性保证功能：已启用 ✅");
  Serial.printf("[MONOTONIC_TEST] 温度容差：%.1f℃ (允许微小下降)\n", TEMP_MONOTONIC_TOLERANCE);
  Serial.printf("[MONOTONIC_TEST] 重置阈值：%.1f℃ (大幅降温时重置)\n", TEMP_RESET_THRESHOLD);
  Serial.printf("[MONOTONIC_TEST] PWM频率：%dHz (高频平滑控制)\n", FIR_FREQ);
  
  // 显示当前温度和基准值
  Serial.printf("[MONOTONIC_TEST] 当前温度：BT=%.2f℃ ET=%.2f℃\n", 
                beanTemperature, exhaustTemperature);
  Serial.printf("[MONOTONIC_TEST] 当前控制：FIR=%d%% FAN=%d%%\n", 
                heaterPowerLevel, fanSpeedLevel);
  
  Serial.println("[MONOTONIC_TEST] 功能说明：");
  Serial.println("[MONOTONIC_TEST] - 在火力/风力不变时，温度只能升高或恒温");
  Serial.println("[MONOTONIC_TEST] - 控制变化时自动重置基准温度");
  Serial.println("[MONOTONIC_TEST] - 大幅降温时认为重新开始烘焙");
  Serial.println("[MONOTONIC_TEST] - 允许0.5℃以内的微小波动");
  
  #else
  Serial.println("[MONOTONIC_TEST] 单调性保证功能：已禁用 ❌");
  Serial.println("[MONOTONIC_TEST] 如需启用，请在user_config.h中定义ENABLE_MONOTONIC_TEMPERATURE");
  #endif
  
  Serial.println("[MONOTONIC_TEST] ================================================");
  Serial.println("[MONOTONIC_TEST] 测试建议：设定固定火力后观察温度变化趋势");
}

bool save_config() {
  StaticJsonDocument<256> doc;
  doc["ct"] = ct;
  doc["Kp"] = Kp;
  doc["Ki"] = Ki;
  doc["Kd"] = Kd;
  
  return save_config_json(doc) > 0;
}

bool load_config() {
  StaticJsonDocument<256> doc;
  if(load_config_json(doc) == 0) {
    // 加载失败，使用默认配置
    ct = CT;
    Kp = PRO;
    Ki = INT;
    Kd = DER;
    // 创建包含默认值的JSON对象并保存
    doc["ct"] = ct;
    doc["Kp"] = Kp;
    doc["Ki"] = Ki;
    doc["Kd"] = Kd;
    save_config_json(doc);
    return false;
  }
  // 加载成功，更新配置
  ct = doc["ct"];
  Kp = doc["Kp"];
  Ki = doc["Ki"];
  Kd = doc["Kd"];
  
  return true;
}
#ifdef PID_CONTROL
// 处理PID命令
void handlePIDCommand(String msg) {
  String cmd = msg.substring(4);  // 去除 "PID;"
  if (cmd.startsWith("ON")) {     // 开启PID
    operatingMode = BT_AUTO;
    myPID.SetMode(AUTOMATIC);
    // SerialBT.println("# PID 开启"); // SPP蓝牙已禁用
    Serial.println("# PID 开启");
#ifdef USE_BLE
    if (bleCharacteristic) {
      bleCharacteristic->setValue("# PID 开启");
      bleCharacteristic->notify();
    }
#endif
  } else if (cmd.startsWith("OFF")) {  // 关闭PID
    operatingMode = SHOUDONG;
    myPID.SetMode(MANUAL);
    // SerialBT.println("# PID 关闭"); // SPP蓝牙已禁用
    Serial.println("# PID 关闭");
#ifdef USE_BLE
    if (bleCharacteristic) {
      bleCharacteristic->setValue("# PID 关闭");
      bleCharacteristic->notify();
    }
#endif
  } else if (cmd.startsWith("GO")) {  // 开始烘焙
    operatingMode = BT_AUTO;
    myPID.SetMode(AUTOMATIC);
    // SerialBT.println("# PID 烘焙开启"); // SPP蓝牙已禁用
    Serial.println("# PID 烘焙开启");
#ifdef USE_BLE
    if (bleCharacteristic) {
      bleCharacteristic->setValue("# PID 烘焙开启");
      bleCharacteristic->notify();
    }
#endif
  } else if (cmd.startsWith("STOP")) {  // 停止烘焙
    operatingMode = SHOUDONG;
    myPID.SetMode(MANUAL);
    heaterPowerLevel = 0;              // 设置功率为0
    fanSpeedLevel = FAN_AUTO_COOL;  // 设置风扇自动冷却
    targetTemperature = 0;              // 设置设定值为0
    isRoastingStarted = false;        // 停止计时器，暂停在当前时间
    // SerialBT.println("# PID 烘焙停止"); // SPP蓝牙已禁用
    Serial.println("# PID 烘焙停止");
#ifdef USE_BLE
    if (bleCharacteristic) {
      bleCharacteristic->setValue("# PID 烘焙停止");
      bleCharacteristic->notify();
    }
#endif
  } else if (cmd.startsWith("SV;")) {
    targetTemperature = cmd.substring(3).toDouble();
    // SV = Setpoint;
    // SerialBT.print("# PID 设定值 = "); // SPP蓝牙已禁用
    // SerialBT.println(Setpoint);
    Serial.print("# PID 设定值 = ");
    Serial.println(targetTemperature);
#ifdef USE_BLE
    if (bleCharacteristic) {
      String msg = "# PID 设定值 = " + String(targetTemperature);
      bleCharacteristic->setValue(msg.c_str());
      bleCharacteristic->notify();
    }
#endif
  } else if (cmd.startsWith("CT;")) {
    ct = cmd.substring(3).toInt();
    myPID.SetSampleTime(ct);
    // SerialBT.print("# PID 周期 (ms) = "); // SPP蓝牙已禁用
    // SerialBT.println(ct);
    Serial.print("# PID 周期 (ms) = ");
    Serial.println(ct);
#ifdef USE_BLE
    if (bleCharacteristic) {
      String msg = "# PID 周期 (ms) = " + String(ct);
      bleCharacteristic->setValue(msg.c_str());
      bleCharacteristic->notify();
    }
#endif
    save_config();  // 保存配置
  } else if (cmd.startsWith("T;")) {
    Kp = cmd.substring(2, 6).toFloat();
    Ki = cmd.substring(7, 11).toFloat();
    Kd = cmd.substring(12).toFloat();
    // 调试代码，这里获取的KP,KI,KD 发送到串口调试
    Serial.print("KP=");
    Serial.print(Kp);
    Serial.print(",KI=");
    Serial.print(Ki);
    Serial.print(",KD=");
    Serial.println(Kd);
    save_config();
  } else if (cmd.startsWith("FANTEST,")) {
    handleFanTestCommand(cmd);  // 处理风扇测试命令
  } else if (cmd.startsWith("PWMDEBUG,")) {
    handlePWMDebugCommand(cmd); // 处理手动PWM调试命令
  } else if (cmd.startsWith("FANRESET")) {
    handleResetFanCommand(cmd); // 处理风扇重置命令
  } else if (cmd.startsWith("PULSESCAN")) {
    handlePulseScanCommand(cmd); // 处理脉宽扫描命令
  } else if (cmd.startsWith("SINGLETEST,")) {
    handleSingleChannelTestCommand(cmd); // 处理单通道测试命令
  } else if (cmd.startsWith("HEATERTEST,")) {
    handleHeaterTestCommand(cmd); // 处理火力测试命令
  } else if (cmd.startsWith("TEMPMONITOR")) {
    handleTempMonitorCommand(cmd); // 处理温度监控命令
  } else if (cmd.startsWith("STABILITYTEST")) {
    handleStabilityTestCommand(cmd); // 处理稳定性测试命令
  } else if (cmd.startsWith("MONOTONICTEST")) {
    handleMonotonicTestCommand(cmd); // 处理温度单调性测试命令
  } else if (cmd.startsWith("PIDCURVE")) {  // PID曲线跟踪命令
    operatingMode = PID_AUTO;
    myPID.SetMode(AUTOMATIC);
    Serial.println("# PID曲线跟踪已启动");
#ifdef USE_BLE
    if (bleCharacteristic) {
      bleCharacteristic->setValue("# PID曲线跟踪已启动");
      bleCharacteristic->notify();
    }
#endif
  } else if (cmd.startsWith("RATELIMITTEST")) {
    //Serial.println("=== 动态RoR平滑系统测试 ===");
     //Serial.printf("动态控制范围: %.0f℃ - %.0f℃\n", ROR_CONTROL_START_TEMP, ROR_CONTROL_END_TEMP);
     //Serial.printf("起始最大变化率: %.1f℃/s (在 %.0f℃)\n", ROR_CONTROL_START_MAX_CHANGE, ROR_CONTROL_START_TEMP);
     //Serial.printf("结束最大变化率: %.1f℃/s (在 %.0f℃)\n", ROR_CONTROL_END_MAX_CHANGE, ROR_CONTROL_END_TEMP);
     //Serial.printf("起始平滑因子: %.2f\n", ROR_CONTROL_START_SMOOTHING);
     //Serial.printf("结束平滑因子: %.2f\n", ROR_CONTROL_END_SMOOTHING);
     //Serial.println("注意: 系统会根据当前温度动态调整限制, 旨在创造自然的烘焙曲线弧度。");
  } else if (cmd.startsWith("SMOOTHNESS,")) {
    float newFactor = cmd.substring(11).toFloat();
    if (newFactor >= 0.1f && newFactor <= 1.0f) {
      // 注意：这个只是显示用，实际需要重新编译固件才能生效
       //Serial.printf("平滑因子设置为: %.2f (需重新编译固件生效)\n", newFactor);
       //Serial.println("平滑因子范围: 0.1(最平滑) - 0.5(较平滑) - 1.0(不平滑)");
    } else {
       //Serial.println("平滑因子范围: 0.1 - 1.0");
    }
  } else if (cmd.startsWith("PIDTEST")) {
    Serial.println("========== PID控制系统测试 ==========");
    Serial.printf("PID模式: %s\n", (myPID.GetMode() == AUTOMATIC) ? "自动" : "手动");
    Serial.printf("当前输入温度: %.2f℃ (豆温BT)\n", beanTemperature);
    Serial.printf("目标温度: %.2f℃\n", targetTemperature);
    Serial.printf("PID输出: %.2f%% (火力)\n", Output);
    Serial.printf("当前火力: %d%%\n", heaterPowerLevel);
    Serial.printf("PID参数: Kp=%.3f Ki=%.3f Kd=%.3f\n", Kp, Ki, Kd);
    Serial.printf("采样时间: %.0fms\n", ct);
    Serial.println("注意: PID控制跟踪豆温(BT)，不是风温(ET)");
    Serial.println("=====================================");
  } else if (cmd.startsWith("RESPONSETEST")) {
    Serial.println("========== 温度响应速度测试 ==========");
    Serial.printf("温度更新间隔: %dms\n", TEMP_UPDATE_INTERVAL);
    Serial.printf("BT卡尔曼滤波: Q=%.3f R=%.3f\n", Q_kalman, R_kalman);
    Serial.printf("ET卡尔曼滤波: Q=%.3f R=%.3f\n", ET_Q_kalman, ET_R_kalman);
    Serial.printf("ROR滤波强度: %d\n", ROR_FILTER);
    #ifdef ENABLE_TEMP_RATE_LIMITER
    Serial.println("变化率限制: 已启用");
    #else
    Serial.println("变化率限制: 已禁用 (提高响应速度)");
    #endif
    #ifdef ENABLE_DYNAMIC_ROR_CONTROL
    Serial.println("动态RoR控制: 已启用");
    #else
    Serial.println("动态RoR控制: 已禁用 (提高响应速度)");
    #endif
    Serial.printf("当前温度: BT=%.2f℃ ET=%.2f℃ ROR=%.2f℃/min\n",
                  beanTemperature, exhaustTemperature, rateOfRise);
    Serial.println("优化说明: 已调整滤波参数以提高响应速度");
    Serial.println("=====================================");
  } else if (cmd.startsWith("PWMFREQ,")) {
    // 动态调整PWM频率测试
    int new_freq = cmd.substring(8).toInt();
    #ifdef USE_OPTIMIZED_PWM_CONTROL
    pwmController.setPWMFrequency(new_freq);
    #endif
  } else if (cmd.startsWith("SMOOTHING,")) {
    // 调整平滑因子
    float new_factor = cmd.substring(10).toFloat();
    #ifdef USE_OPTIMIZED_PWM_CONTROL
    pwmController.setSmoothingFactor(new_factor);
    #endif
  } else if (cmd.startsWith("RATELIMIT,")) {
    // 调整功率变化率限制
    int new_limit = cmd.substring(10).toInt();
    #ifdef USE_OPTIMIZED_PWM_CONTROL
    pwmController.setPowerChangeLimit(new_limit);
    #endif
  } else if (cmd.startsWith("LINEARTABLE")) {
    // 显示线性化查找表
    #ifdef USE_OPTIMIZED_PWM_CONTROL
    pwmController.printLinearizationTable();
    #endif
  } else if (cmd.startsWith("POWERVERIFY")) {
    // 功率验证测试 - 测试关键功率点的准确性
    #ifdef USE_OPTIMIZED_PWM_CONTROL
    Serial.println("========== 功率准确性验证测试 ==========");
    Serial.printf("测试%.0fW发热丝的功率线性度...\n", pwmController.getHeaterWattage());

    int test_powers[] = {1, 5, 10, 15, 18, 20, 25, 50, 75, 100};
    int num_tests = sizeof(test_powers) / sizeof(test_powers[0]);

    for (int i = 0; i < num_tests; i++) {
      int power = test_powers[i];
      pwmController.setPower(power);
      delay(100);

      float linear_coeff = pwmController.linearizePWMPower(power);
      int pwm_value = pwmController.calculatePWMValue();
      float expected_watts = pwmController.getHeaterWattage() * linear_coeff;

      Serial.printf("%2d%% -> 系数:%.3f -> PWM:%4d -> 预期功率:%4.0fW\n",
                    power, linear_coeff, pwm_value, expected_watts);
    }
    
    pwmController.setPower(0);  // 测试完成后关闭
    Serial.println("==========================================");
    Serial.println("基于实测数据优化：9%起步，13%不再跳满功率");
    #endif
  } else if (cmd.startsWith("TRIACTEST")) {
    // 可控硅特性测试 - 基于你的实测数据
    #ifdef USE_OPTIMIZED_PWM_CONTROL
    Serial.println("========== 可控硅特性验证测试 ==========");
    Serial.println("最新实测：12%→700W, 14%→800W, 16%→1100W，需强力压制");
    
    int critical_points[] = {10, 11, 12, 13, 14, 15, 16, 17, 18, 20};
    int num_points = sizeof(critical_points) / sizeof(critical_points[0]);
    
    for (int i = 0; i < num_points; i++) {
      int power = critical_points[i];
      pwmController.setPower(power);
      delay(200);  // 给更多时间稳定
      
      float linear_coeff = pwmController.linearizePWMPower(power);
      int pwm_value = pwmController.calculatePWMValue();
      float expected_watts = 1200.0f * linear_coeff;
      
      if (power < 10) {
        Serial.printf("%2d%% -> 死区 -> PWM:%4d -> 预期功率:%4.0fW (应该0W)\n", 
                      power, pwm_value, expected_watts);
      } else if (power == 12) {
        Serial.printf("%2d%% -> 问题点 -> PWM:%4d -> 预期功率:%4.0fW (实测700W→目标150W)\n", 
                      power, pwm_value, expected_watts);
      } else if (power == 14) {
        Serial.printf("%2d%% -> 问题点 -> PWM:%4d -> 预期功率:%4.0fW (实测800W→目标210W)\n", 
                      power, pwm_value, expected_watts);
      } else if (power == 16) {
        Serial.printf("%2d%% -> 问题点 -> PWM:%4d -> 预期功率:%4.0fW (实测1100W→目标270W)\n", 
                      power, pwm_value, expected_watts);
      } else {
        Serial.printf("%2d%% -> 压制区 -> PWM:%4d -> 预期功率:%4.0fW\n", 
                      power, pwm_value, expected_watts);
      }
    }
    
    pwmController.setPower(0);
    Serial.println("==========================================");
    Serial.println("关键改进：1Hz PWM频率 + 可控硅特性补偿");
    #endif
  } else if (cmd.startsWith("HEATER1600")) {
    // 1600W发热丝测试命令
    #ifdef USE_OPTIMIZED_PWM_CONTROL
    Serial.println("========== 1600W发热丝测试 ==========");
    Serial.printf("发热丝功率: %.0fW\n", pwmController.getHeaterWattage());
    Serial.printf("安全模式: %s\n", pwmController.isSafetyModeEnabled() ? "启用" : "禁用");
    Serial.printf("最大功率限制: %.1f%%\n", pwmController.getMaxSafePowerRatio() * 100);

    // 测试关键功率点
    int test_powers[] = {0, 10, 15, 20, 25, 30, 20, 10, 0};
    for (int i = 0; i < 9; i++) {
      Serial.printf("测试功率: %d%%\n", test_powers[i]);
      pwmController.setPower(test_powers[i]);
      delay(2000);

      float coeff = pwmController.linearizePWMPower(test_powers[i]);
      float actual_watts = pwmController.getHeaterWattage() * coeff;
      Serial.printf("  结果: %d%% -> %.0fW (%.1f%%实际功率)\n",
                   test_powers[i], actual_watts, coeff * 100);
    }
    Serial.println("✅ 1600W发热丝测试完成");
    #endif
  } else if (cmd.startsWith("HEATERCONFIG")) {
    // 显示发热丝配置信息
    #ifdef USE_OPTIMIZED_PWM_CONTROL
    pwmController.printHeaterConfig();
    #endif
#ifdef USE_OPTIMIZED_PWM_CONTROL
  } else if (cmd.startsWith("PWMTEST")) {
    Serial.println("========== PWM控制线性度测试 ==========");
    testPWMControlLinearity();
  } else if (cmd.startsWith("STABILITYTEST")) {
    Serial.println("========== 功率稳定性测试 ==========");
    testPowerStability();
#endif
  }
}
#endif

#ifdef USE_OPTIMIZED_PWM_CONTROL
// PWM控制测试函数 - 验证线性度
void testPWMControlLinearity() {
    Serial.println("功率% | 线性化系数 | PWM值 | 实际占空比%");
    
    for (int power = 0; power <= 100; power += 10) {
        pwmController.setPower(power);
        delay(100);  // 等待稳定
        
        float linear_coeff = pwmController.linearizePWMPower(power);
        int pwm_value = pwmController.calculatePWMValue();
        
        Serial.printf("%3d%% | %8.3f | %8d | %4d\n", 
                      power, linear_coeff, pwm_value, (int)(linear_coeff * 4095));
    }
    
    Serial.println("==========================================");
    Serial.println("测试完成。检查PWM线性化系数的平滑过渡。");
}

// 功率稳定性测试
void testPowerStability() {
    int test_power = 50;  // 测试50%功率的稳定性
    Serial.printf("测试功率: %d%%，持续5秒\n", test_power);
    
    pwmController.setPower(test_power);
    stabilityMonitor.reset();
    
    unsigned long start_time = millis();
    int sample_count = 0;
    
    while (millis() - start_time < 5000) {  // 5秒测试
        pwmController.update();
        
        if (sample_count % 100 == 0) {  // 每100次循环记录一次
            int current_power = pwmController.getCurrentPower();
            stabilityMonitor.addPowerData(current_power);
            
            if (sample_count % 1000 == 0) {  // 每1000次循环输出一次
                float variation = stabilityMonitor.getPowerVariation();
                Serial.printf("时间: %4lus, 功率: %d%%, 变化率: %.2f%%\n", 
                              (millis() - start_time) / 1000, current_power, variation);
            }
        }
        
        sample_count++;
        delay(1);
    }
    
    bool is_stable = stabilityMonitor.isPowerStable();
    float final_variation = stabilityMonitor.getPowerVariation();
    
    Serial.printf("最终变化率: %.2f%%\n", final_variation);
    
    if (is_stable && final_variation < 1.0f) {
        Serial.println("✅ 稳定性测试通过！功率输出稳定。");
    } else {
        Serial.println("⚠️  稳定性需要改善。");
    }
}
#endif

// 本地跑曲线--------------------------------------------------------
int runCurveIndex = 0; // 运行曲线当前索引，全局变量

void run_curve() {
    static bool first_run = true;
    static unsigned long start_time = 0;
    unsigned long current_time;

    if (operatingMode == AUTO) {
        if(runCurvePoint <= 0) {
            Serial.printf("[AUTO_RUN_CURVE] 错误: 运行曲线数据点数量无效(%d)\n", runCurvePoint);
            operatingMode = SHOUDONG;
            return;
        }
        
        // 第一次运行时，记录开始时间并设置初始值
        if (first_run) {
            start_time = millis() / 1000;  // 转换为秒
            Serial.printf("[AUTO_RUN_CURVE] 开始运行曲线，总点数=%d\n", runCurvePoint);
            fanSpeedLevel = runCurve[0].fan;
            heaterPowerLevel = runCurve[0].fir;
            runCurveIndex = 1;
            first_run = false;
            Serial.printf("[AUTO_RUN_CURVE] 设置初始值: FIR=%d%%, FAN=%d%%\n", heaterPowerLevel, fanSpeedLevel);
            return;
        }
        
        // 计算当前运行时间并与曲线点比较
        current_time = (millis() / 1000) - start_time;
        if (runCurveIndex < runCurvePoint && current_time >= runCurve[runCurveIndex].t) {
            fanSpeedLevel = runCurve[runCurveIndex].fan;
            heaterPowerLevel = runCurve[runCurveIndex].fir;
            Serial.printf("[AUTO_RUN_CURVE] 时间=%lu秒 设置新值: FIR=%d%%, FAN=%d%% (点%d/%d)\n", 
                         current_time, heaterPowerLevel, fanSpeedLevel, runCurveIndex+1, runCurvePoint);
            runCurveIndex++;
        }
        
        if (runCurveIndex >= runCurvePoint) {
            Serial.println("[AUTO_RUN_CURVE] 曲线运行完成，切换回手动模式");
            operatingMode = SHOUDONG;
            //发送对应变化到串口屏
            Serial2.printf("page0.b1.txt=\"本机自动\"\xff\xff\xff");
            Serial2.printf("page0.c0.val=1\xff\xff\xff");
            Serial2.printf("page0.c1.val=0\xff\xff\xff");
            first_run = true;
        }
    }
}

// PID曲线跟踪功能--------------------------------------------------------
int pidCurveIndex = 0; // PID曲线跟踪当前索引

void run_pid_curve() {
    static bool first_run = true;
    static unsigned long start_time = 0;
    unsigned long current_time;

    if (operatingMode == PID_AUTO) {
        if(runCurvePoint <= 0) {
            Serial.printf("[PID_CURVE] 错误: 曲线数据点数量无效(%d)\n", runCurvePoint);
            operatingMode = SHOUDONG;
            return;
        }
        
        // 第一次运行时，记录开始时间并设置初始值
        if (first_run) {
            start_time = millis() / 1000;  // 转换为秒
            Serial.printf("[PID_CURVE] 开始PID曲线跟踪，总点数=%d\n", runCurvePoint);
            targetTemperature = runCurve[0].bt;  // 设置初始目标温度
            fanSpeedLevel = runCurve[0].fan;     // 设置初始风力
            myPID.SetMode(AUTOMATIC);            // 启用PID
            pidCurveIndex = 1;
            first_run = false;
            isRoastingStarted = true;            // 标记烘焙开始
            Serial.printf("[PID_CURVE] 初始设置: 目标温度=%.1f℃, 风力=%d%%\n", targetTemperature, fanSpeedLevel);
            return;
        }
        
        // 计算当前运行时间并与曲线点比较
        current_time = (millis() / 1000) - start_time;
        
        // 平滑温度过渡，避免跳变
        if (pidCurveIndex < runCurvePoint && current_time >= runCurve[pidCurveIndex].t) {
            float old_target = targetTemperature;
            targetTemperature = runCurve[pidCurveIndex].bt;
            fanSpeedLevel = runCurve[pidCurveIndex].fan;
            
            Serial.printf("[PID_CURVE] 时间=%lu秒 更新目标: %.1f℃→%.1f℃, 风力=%d%% (点%d/%d)\n", 
                         current_time, old_target, targetTemperature, fanSpeedLevel, 
                         pidCurveIndex+1, runCurvePoint);
            pidCurveIndex++;
        }
        
        if (pidCurveIndex >= runCurvePoint) {
            Serial.println("[PID_CURVE] PID曲线跟踪完成，切换回手动模式");
            operatingMode = SHOUDONG;
            myPID.SetMode(MANUAL);
            heaterPowerLevel = 0;           // 停止加热
            fanSpeedLevel = FAN_AUTO_COOL;  // 自动冷却
            isRoastingStarted = false;      // 停止计时
            //发送对应变化到串口屏
            Serial2.printf("page0.b1.txt=\"本机自动\"\xff\xff\xff");
            Serial2.printf("page0.c0.val=1\xff\xff\xff");
            Serial2.printf("page0.c1.val=0\xff\xff\xff");
            first_run = true;
        }
    }
}
// 记录曲线数据------------------------------------------------------------
void record_curve() {
    // 检查是否达到记录曲线数据的时间间隔
    if (millis() - lastCurveRecordTime > CURVE_RECORD_INTERVAL || (!isRoastingStarted && recordCurveIndex > 0)) {
        lastCurveRecordTime = millis();  // 更新上次记录曲线数据的时间戳
    
        // 如果在烘培状态下，定期记录数据点，确保曲线连续性
        if (isRoastingStarted && operatingMode != AUTO) {
            // 判断是否需要记录数据点
            // 情况1：火力或风力发生明显变化 (超过1%)
            // 情况2：温度发生明显变化 (超过5℃)
            // 情况3：已经超过20秒钟没有记录数据点
            // 情况4：曲线索引为0，记录第一个点
    
            static float last_recorded_BT = 0;
            static unsigned long last_point_time = 0;
    
            // 修改为整型变量，用于记录触发原因
            int should_record = 0; // 0=不记录，1=火力/风力变化，2=温度变化，3=超时，4=第一个点
            
            // 火力或风力变化超过1%时记录
            if (abs(previousHeaterPowerLevel - heaterPowerLevel) > 1 || abs(previousFanSpeedLevel - fanSpeedLevel) > 1) {
                should_record = 1;
            }
    
            // 温度变化超过5℃时记录
            if (abs(beanTemperature - last_recorded_BT) >= 5.0) {
                should_record = 2;
            }
    
            // 超过20秒没记录时强制记录一个点
            if (millis() - last_point_time > 20000) {
                should_record = 3;
            }
    
            // 第一个点必须记录
            if (recordCurveIndex == 0) {
                should_record = 4;
            }
            if (should_record > 0) {
                if (recordCurveIndex < CURVE_RECORD_BUFFER_SIZE) {
                    recordCurve[recordCurveIndex].t = roastingTime;
                    recordCurve[recordCurveIndex].fir = heaterPowerLevel;
                    recordCurve[recordCurveIndex].fan = fanSpeedLevel;
                    recordCurve[recordCurveIndex].bt = beanTemperature;
                    recordCurveIndex++;
    
                    last_recorded_BT = beanTemperature;
                    last_point_time = millis();
    
                    // 更新前一次值用于下次比较
                    previousHeaterPowerLevel = heaterPowerLevel;
                    previousFanSpeedLevel = fanSpeedLevel;
    
                    // 调试信息，包含触发原因
                    const char* reason[] = {"", "火力/风力变化", "温度变化", "超时强制记录", "第一个点"};
                    Serial.printf("[RECORD_CURVE] 记录点#%d: 时间=%lu秒 | 原因=%s | BT=%.1f℃ | FIR=%d%% | FAN=%d%%\n",
                      recordCurveIndex, roastingTime, reason[should_record], beanTemperature, heaterPowerLevel, fanSpeedLevel);
                    // 调试信息
                    
                } else {
                    Serial.println("[RECORD_CURVE] 警告：曲线数组已满，无法记录更多数据点");
                }
            }
    
            // 烘培结束时强制记录最后一点（FIR=0, FAN=0）
            if (!isRoastingStarted && recordCurveIndex > 0 && (millis() - lastCurveRecordTime > 1000)) {
                // 最后一点尚未记录为0，则强制写入
                if (recordCurve[recordCurveIndex - 1].fir != 0 || recordCurve[recordCurveIndex - 1].fan != 0) {
                    if (recordCurveIndex < CURVE_RECORD_BUFFER_SIZE) {
                        recordCurve[recordCurveIndex].t = roastingTime;
                        recordCurve[recordCurveIndex].fir = 0;  // 设置火力为0
                        recordCurve[recordCurveIndex].fan = 0;  // 设置风力为0
                        recordCurve[recordCurveIndex].bt = beanTemperature;
                        recordCurveIndex++;  // 数组指针加1

                        // 调试信息
                        Serial.printf("[CURVE] 强制记录最终点#%d: 时间=%lu秒 BT=%.1f FIR=0 FAN=0\n",
                                        recordCurveIndex, roastingTime, beanTemperature);
                    } else {
                        Serial.println("[CURVE] 警告：曲线数组已满，无法记录最终点");
                    }
                }
            }
        }
    }
}
// -----------------------------------------------------------------
// 解析并执行普通串行命令---------------------------------------------
/*使用时可以通过串口发送以下格式的命令：
- 保存曲线： SAVE_CURVE;{"info":{"id":1,"description":"测试曲线"},"data":[{"t":0,"bt":100,"fir":20,"fan":30}]}
- 读取曲线： READ_CURVE1 或 READ_CURVE 1
*/
void check_Serial() {
  String stmp;
  if (Serial.available() > 0) {
    String msg = Serial.readStringUntil('\n');
    msg.trim(); // 去除前后空格
    
    // 处理SAVE_CURVE命令
    if (msg.indexOf("SAVE_CURVE") == 0) {
      String jsonData = msg.substring(10); // 获取JSON数据部分
      if (save_json_string_to_file(jsonData)) {
        Serial.println("曲线数据保存成功");
      } else {
        Serial.println("曲线数据保存失败");
      }
    }
    
    // 处理READ_CURVE命令
    if (msg.indexOf("READ_CURVE") == 0) {
        String curveIdStr = msg.substring(10);
        curveIdStr.trim(); // 去除空格
        int curveId = curveIdStr.toInt();
        
        if (send_curve_to_serial(Serial, curveId)) {  // 修改为传入Serial对象
            Serial.println("曲线数据发送成功");
        } else {
            Serial.println("曲线数据发送失败");
        }
    }
    
    // 处理FANTEST命令
    if (msg.indexOf("FANTEST,") == 0) {
        handleFanTestCommand(msg);
    }
    
    // 处理PWMDEBUG命令
    if (msg.indexOf("PWMDEBUG,") == 0) {
        handlePWMDebugCommand(msg);
    }
    
    // 处理FANRESET命令
    if (msg.indexOf("FANRESET") == 0) {
        handleResetFanCommand(msg);
    }
    
    // 处理PULSESCAN命令
    if (msg.indexOf("PULSESCAN") == 0) {
        handlePulseScanCommand(msg);
    }

    // 处理PIDTEST命令
    if (msg.indexOf("PIDTEST") == 0) {
        handlePIDCommand("PID;PIDTEST");
    }

    // 处理RESPONSETEST命令
    if (msg.indexOf("RESPONSETEST") == 0) {
        handlePIDCommand("PID;RESPONSETEST");
    }
    
#ifdef USE_OPTIMIZED_PWM_CONTROL
    // 处理PWM控制测试命令
    if (msg.indexOf("PWMTEST") == 0) {
        handlePIDCommand("PID;PWMTEST");
    }
    
    if (msg.indexOf("STABILITYTEST") == 0) {
        handlePIDCommand("PID;STABILITYTEST");
    }
#endif
  }
}
// -----------------------------------------------------------------
// 解析并执行蓝牙串行命令---------------------------------------------
void check_BT_Serial() {
  
  // SPP蓝牙已禁用，此函数只处理BLE蓝牙通信
  
  // 以下SPP蓝牙代码已注释
  /*
  String stmp;

  // 处理SPP蓝牙通信
  if (SerialBT.available() > 0) {
    String msg = SerialBT.readStringUntil('\n');
    msg.trim();
    
    // 处理SAVE_CURVE命令
    if (msg.indexOf("SAVE_CURVE") == 0) {
      String jsonData = msg.substring(10);
      if (save_json_string_to_file(jsonData)) {
        SerialBT.println("曲线数据保存成功");
      } else {
        SerialBT.println("曲线数据保存失败");
      }
    }
    
    // 处理READ_CURVE命令
    if (msg.indexOf("READ_CURVE") == 0) {
      String curveIdStr = msg.substring(10);
      curveIdStr.trim();
      int curveId = curveIdStr.toInt();
      
      if (send_curve_to_serial(SerialBT, curveId)) {
        SerialBT.println("曲线数据发送成功");
      } else {
        SerialBT.println("曲线数据发送失败");
      }
    }
    
    if (msg.indexOf("CHAN;") == 0) {
      SerialBT.println("#OK");
    } else if (msg.indexOf("UNITS;") == 0) {
      if (msg.length() > 6 && msg.substring(6, 7) == "F") {
        useCelsiusScale = false;
      }
    } else if (msg.indexOf("READ") == 0) {
      logger();
    } else if (msg.indexOf("OT1") == 0) {
      handleOT1Command(msg);
    } else if (msg.indexOf("DCFAN") == 0) {
      handleOT3Command(msg);
    } else if (msg.indexOf("PID;") == 0) {
      handlePIDCommand(msg);
    }
  }
  */

  // 处理BLE蓝牙通信
#ifdef USE_BLE
  if (bleCharacteristic && bleCharacteristic->getValue().length() > 0) {
    String msg = bleCharacteristic->getValue().c_str();

    // 处理SAVE_CURVE命令
    if (msg.indexOf("SAVE_CURVE") == 0) {
      String jsonData = msg.substring(10);
      if (save_json_string_to_file(jsonData)) {
        bleCharacteristic->setValue("曲线数据保存成功");
        bleCharacteristic->notify();
      } else {
        bleCharacteristic->setValue("曲线数据保存失败");
        bleCharacteristic->notify();
      }
    }
    
    // 处理READ_CURVE命令
    if (msg.indexOf("READ_CURVE") == 0) {
      String curveIdStr = msg.substring(10);
      curveIdStr.trim();
      int curveId = curveIdStr.toInt();
      
      if (send_curve_to_serial(bleCharacteristic, curveId)) {
        bleCharacteristic->setValue("曲线数据发送成功");
        bleCharacteristic->notify();
      } else {
        bleCharacteristic->setValue("曲线数据发送失败");
        bleCharacteristic->notify();
      }
    }

    if (msg.indexOf("CHAN;") == 0) {
      bleCharacteristic->setValue("#OK");
      bleCharacteristic->notify();
    } else if (msg.indexOf("UNITS;") == 0) {
      if (msg.length() > 6 && msg.substring(6, 7) == "F") {
        useCelsiusScale = false;
      }
    } else if (msg.indexOf("READ") == 0) {
      logger();
    } else if (msg.indexOf("OT1") == 0) {
      handleOT1Command(msg);
    } else if (msg.indexOf("DCFAN") == 0) {
      handleOT3Command(msg);
    } else if (msg.indexOf("PID;") == 0) {
      handlePIDCommand(msg);
    }
  }
#endif
}
//-----------------------------------------------------------------
// 替代舵机库，使用LEDC控制舵机和无刷ESC
void setServoAngle(int channel, int angle) {
  int pulseWidth;
  
  // 区分舵机控制和无刷ESC控制
  if (channel == SERVO2_CHAN && !USE_BEAN_SERVO) {
    // 无刷风扇ESC控制：使用完整范围 500-2500us
    pulseWidth = map(angle, 0, 180, 500, 2500);
    
    // 调试输出
    static int last_angle = -1;
    if (angle != last_angle) {
      Serial.printf("[SERVO_ESC] 扩展ESC控制：角度%d° -> 脉宽%dus\n", angle, pulseWidth);
      last_angle = angle;
    }
  } else {
    // 普通舵机控制：保持原有范围 1000-1900us
    pulseWidth = map(angle, 0, 180, 1000, 1900);
  }
  
  int dutyCycle = (pulseWidth * 4096) / 20000;  
  dutyCycle = constrain(dutyCycle, 0, 4095);
  ledcWriteChannel(channel, dutyCycle);            
}
// 输出风力和火力以及LED控制信号--------------------------------------------
void outPWM_update() {
  static unsigned long last_update = 0;
  static int last_heater_power = -1;
  
  // 安全检查：风力过低时关闭火力
  if (fanSpeedLevel < HTR_CUTOFF_FAN_VAL) {
    heaterPowerLevel = 0;
  }
  
  // 限制更新频率，避免过于频繁的调整
  if (millis() - last_update < 50) {  // 50ms更新一次
    return;
  }
  last_update = millis();
  
  // 检测功率变化并应用改进的相位控制
  if (heaterPowerLevel != last_heater_power) {
    Serial.printf("[POWER_CHANGE] 火力调整: %d%% -> %d%%\n", 
                  last_heater_power, heaterPowerLevel);
    last_heater_power = heaterPowerLevel;
  }
  
#ifdef USE_OPTIMIZED_PWM_CONTROL
  // 使用优化的PWM控制处理火力输出
  pwmController.setPower(heaterPowerLevel);
  
  // 更新稳定性监控
  stabilityMonitor.addPowerData(heaterPowerLevel);
  
  // 定期输出调试信息
  static unsigned long last_debug = 0;
  if (millis() - last_debug > 10000) {  // 每10秒输出一次
    last_debug = millis();
    
    if (heaterPowerLevel > 0) {
      pwmController.printDebugInfo();
      
      if (stabilityMonitor.isPowerStable()) {
        Serial.printf("[STABILITY] 功率稳定，变化率: %.2f%%\n", 
                      stabilityMonitor.getPowerVariation());
      } else {
        Serial.printf("[STABILITY] 功率调整中，变化率: %.2f%%\n", 
                      stabilityMonitor.getPowerVariation());
      }
    }
  }
  
  // 更新PWM控制
  pwmController.update();
#else
  // 原有的简单PWM控制（作为备用）
  int out_fir = map(heaterPowerLevel, 0, 100, 0, 4095);
  ledcWriteChannel(FIR_PWM_CHAN, out_fir);
#endif
  
  // 风扇控制保持原有逻辑
  updateFanControl();
  
  // LED控制保持原有逻辑
  updateLEDControl();
}

// 风扇控制函数（从原outPWM_update中分离出来）
void updateFanControl() {
  // 风扇智能映射：将用户的0-100%映射到硬件实际可调的0-62%
  int effective_fan_speed = map(fanSpeedLevel, 0, 100, 0, FAN_HARDWARE_LIMIT);
  
  // PWM控制使用映射后的风速
  int out_fan = map(effective_fan_speed, 0, 100, 0, 4095);
  ledcWriteChannel(FAN_PWM_CHAN, out_fan);
  
  // 舵机控制也使用映射后的风速
  int servo_angle = map(effective_fan_speed, 0, 100, 0, SERVO_MAX);
  setServoAngle(SERVO_CHAN, servo_angle);
  if (!USE_BEAN_SERVO) {
    setServoAngle(SERVO2_CHAN, servo_angle);
  }
  
  // 调试信息：显示映射关系
  static int last_fan_level = -1;
  if (fanSpeedLevel != last_fan_level && fanSpeedLevel > 0) {
    Serial.printf("[FAN_MAPPING] 用户设置:%d%% -> 实际输出:%d%% | PWM:%d | 角度:%d°\n", 
                  fanSpeedLevel, effective_fan_speed, out_fan, servo_angle);
    last_fan_level = fanSpeedLevel;
  }
}

// LED控制函数（从原outPWM_update中分离出来）
void updateLEDControl() {
  int out_led_whte = map(whiteLedBrightness, 0, 100, 0, 4095);
  ledcWriteChannel(LED_PWM_CHAN, out_led_whte);
  int out_led_yellow = map(yellowLedBrightness, 0, 100, 0, 4095);
  ledcWrite(OUT_LED_YELLOW, out_led_yellow);
}
//-----------------------------------------------------------------------

class MyServerCallbacks: public BLEServerCallbacks {
    void onConnect(BLEServer* pServer) {
      Serial.println("BLE Client Connected");
    };

    void onDisconnect(BLEServer* pServer) {
      Serial.println("BLE Client Disconnected");
      BLEDevice::startAdvertising(); // 在断开连接后重新开始广播
    }
};

void init_BLE() {
#ifdef USE_BLE
  // 初始化BLE设备
  BLEDevice::init("BUU4.0");
  
  // 创建BLE服务器
  pBLEServer = BLEDevice::createServer();
  pBLEServer->setCallbacks(new MyServerCallbacks());
  
  // 创建BLE服务
  BLEService *pService = pBLEServer->createService(SERVICE_UUID);
  
  // 创建BLE特性
  bleCharacteristic = pService->createCharacteristic(
    CHARACTERISTIC_UUID,
    BLECharacteristic::PROPERTY_READ |
    BLECharacteristic::PROPERTY_WRITE |
    BLECharacteristic::PROPERTY_NOTIFY
    );
    // 添加BLE2902描述符，确保通知功能正常工作
  // 设置访问权限
  bleCharacteristic->setAccessPermissions(ESP_GATT_PERM_READ | ESP_GATT_PERM_WRITE);

  // 创建pNotifyCharacteristic并添加描述符
  pNotifyCharacteristic = pService->createCharacteristic(
      NOTIFY_CHARACTERISTIC_UUID,
      BLECharacteristic::PROPERTY_NOTIFY
  );
  pNotifyCharacteristic->addDescriptor(new BLE2902());
  bleCharacteristic->addDescriptor(new BLE2902());
  
  // 启动服务
  pService->start();
  
  // 配置广播
  BLEAdvertising *pAdvertising = BLEDevice::getAdvertising();
  
  // 添加服务UUID到广播数据
  pAdvertising->addServiceUUID(SERVICE_UUID);
  
  // 启用扫描响应
  pAdvertising->setScanResponse(true);
  
  // 设置连接间隔参数 - 使用更短的间隔提高可见性
  // 设置连接间隔参数 - 使用更短的间隔提高可见性
  // 广播间隔 (Advertising Interval) 是指两个连续广播事件之间的时间间隔。较短的广播间隔可以使设备更快地被扫描设备发现，但会增加功耗。
  // 默认情况下，ESP32的BLE广播是持续进行的，没有内置的超时机制使其自动停止。
  // 如果设备在一段时间不活动后无法被发现，可能是因为手机应用端没有正确处理BLE设备的断开和重连，
  // 或者设备进入了某种低功耗模式，导致广播间隔变大或停止。
  // 为了延长设备的可搜索时间，我将尝试调整BLE广播间隔，使其更频繁地广播，这可能会增加功耗，但能提高设备的可见性。
  // 最小广播间隔 (Min Advertising Interval) 和 最大广播间隔 (Max Advertising Interval) 的单位是 0.625 毫秒。
  // 0x20 (32) * 0.625ms = 20ms
  // 0x40 (64) * 0.625ms = 40ms
  pAdvertising->setMinPreferred(0x20);  // 设置最小连接间隔为20ms
  pAdvertising->setMaxPreferred(0x40);  // 设置最大连接间隔为40ms
  
  // 设置广播数据
  BLEAdvertisementData advData;
  advData.setFlags(0x06); // 0x06 = (ESP_BLE_ADV_FLAG_GEN_DISC | ESP_BLE_ADV_FLAG_BREDR_NOT_SPT)
  advData.setName("BUU4.0"); // 在广播数据中添加设备名称
  advData.setCompleteServices(BLEUUID(SERVICE_UUID)); // 添加完整服务UUID
  pAdvertising->setAdvertisementData(advData);
  
  // 设置扫描响应数据
  BLEAdvertisementData scanResponse;
  scanResponse.setName("BUU4.0");
  scanResponse.setCompleteServices(BLEUUID(SERVICE_UUID));
  
  // 添加制造商数据以增加可见性
  uint8_t manData[4] = {0x42, 0x55, 0x33, 0x30}; // 自定义制造商数据 (BU30)
  String manufacturerData = "BU30";
  scanResponse.setManufacturerData(manufacturerData);
  pAdvertising->setScanResponseData(scanResponse);
  
  // 启动广播
  BLEDevice::startAdvertising();
  Serial.println("BLE蓝牙初始化成功，已优化广播设置以提高可见性");
  Serial.println("已禁用SPP蓝牙，仅使用BLE蓝牙模式");
#endif
}
//-----------------------------------------------------------------------
// 初始化曲线
void init_curve() {
  // 尝试加载默认曲线到runCurve数组
  if(!load_curve(DEFAULT_CURVE_ID, runCurve, &runCurvePoint)) {  // 修改为直接加载到runCurve
    Serial.printf("[INIT_CURVE] %d号曲线不存在，创建默认曲线\n", DEFAULT_CURVE_ID);
    
    // 使用预定义的默认曲线
    memcpy(recordCurve, DEFAULT_CURVE, sizeof(DEFAULT_CURVE));
    recordCurveIndex = sizeof(DEFAULT_CURVE)/sizeof(DEFAULT_CURVE[0]);
    roastDescription = DEFAULT_CURVE_DESC;
    
    // 保存为默认曲线
    if(save_curve(DEFAULT_CURVE_ID, recordCurve, recordCurveIndex)) {
      Serial.printf("[INIT_CURVE] 默认曲线已保存为%d号曲线\n", DEFAULT_CURVE_ID);
    } else {
      Serial.println("[INIT_CURVE] 保存默认曲线失败");
    }
  }
}

// 系统初始化函数-------------------------------------------------------
void setup() {
  delay(500);
  // 初始化串口
  Serial.begin(115200);
  // 初始化显示
  display_init(); 
  // 初始化MLX90614
  mlx.begin();  

  // 初始化输入输出引脚
  pinMode(OUT_DC_FAN, OUTPUT);
  pinMode(OUT_FIR, OUTPUT);
  pinMode(OUT_LED_WHTE, OUTPUT);
  pinMode(OUT_LED_YELLOW, OUTPUT);
  pinMode(SERVO_PIN, OUTPUT);

  pinMode(IN_FIR_POT, INPUT);
  pinMode(IN_FAN_POT, INPUT);
  pinMode(IN_LED_POT, INPUT);

  // 选择按钮使用内部上拉
  // TODO: 可在此处重新启用按钮
  // pinMode(BUTTON_BT, INPUT_PULLUP);

  // 只初始化BLE蓝牙模式，禁用SPP模式
  Serial.println("仅启用BLE蓝牙模式，禁用SPP模式");
  
  // SerialBT.begin("BUU3.0", true, 1234);  // 禁用SPP蓝牙初始化

  Serial.println("SPP蓝牙已禁用");
  
  init_BLE(); // 初始化BLE蓝牙

  analogReadResolution(12);        // 确保12位分辨率
  analogSetAttenuation(ADC_11db);  // 正确设置衰减

  // 初始化LEDC PWM 通道 - 火力PWM已升级为12位分辨率
  ledcAttachChannel(OUT_DC_FAN, FAN_FREQ, FAN_RESULUTION, FAN_PWM_CHAN);    // 初始化风扇PWM (1600Hz)
  ledcAttachChannel(OUT_FIR, FIR_FREQ, FIR_RESOLUTION, FIR_PWM_CHAN);       // 初始化 FIR PWM (12位分辨率)
  ledcAttachChannel(OUT_LED_WHTE, LED_FREQ, LED_RESULUTION, LED_PWM_CHAN);  // 初始化LED PWM
  ledcAttachChannel(OUT_LED_YELLOW, LED_FREQ, LED_RESULUTION, LED_PWM_CHAN);
  ledcAttachChannel(SERVO_PIN, SERVO_FREQ, SERVO_RES, SERVO_CHAN);          // 舵机1
  ledcAttachChannel(SERVO2_PIN, SERVO_FREQ, SERVO_RES, SERVO2_CHAN);          // 舵机2

  // 初始化优化的PWM控制系统
#ifdef USE_OPTIMIZED_PWM_CONTROL
  // 配置1600W发热丝参数
  pwmController.setHeaterWattage(1600.0f);
  pwmController.setHeaterType(1600);
  pwmController.setSafetyMode(true);
  pwmController.setMaxSafePowerRatio(0.85f);  // 最大85%功率保护

  pwmController.begin();
  Serial.println("========================================");
  Serial.println("系统初始化完成 - 1600W发热丝优化版：");
  Serial.println("- 发热丝：1600W高功率发热丝，功率提升33%");
  Serial.println("- 安全保护：最大85%功率限制(1360W)");
  Serial.println("- 火力控制：高频PWM控制，专为无过零检测优化");
  Serial.printf("- 火力PWM：%dHz高频，增强平滑控制\n", FIR_FREQ);
  Serial.println("- 多层平滑：15点移动平均 + 指数平滑");
  Serial.println("- 变化率限制：防止功率跳变");
  Serial.println("- 测试命令：PID;PWMTEST, PID;STABILITYTEST");
  Serial.println("- 相位控制：软件过零检测 + 线性化算法");
  Serial.println("- 功率平滑：移动平均滤波，减少跳变");
  Serial.println("- 稳定性监控：实时监控功率变化率");

  // 打印1600W发热丝配置信息
  pwmController.printHeaterConfig();
#else
  Serial.println("========================================");
  Serial.println("系统初始化完成 - 标准PWM控制版：");
  Serial.printf("- 火力PWM：%dHz频率，12位分辨率(4096级)\n", FIR_FREQ);
#endif
  Serial.println("- 风扇PWM：1600Hz频率，12位分辨率(4096级)");
  Serial.println("- 死区阈值：1.5 (降低50%改善低电位器响应)");
  Serial.printf("- 20%%电位器位置：%d级PWM (提升16倍精度)\n", (int)(4096 * 0.2));
  Serial.println("- 安全风力阈值：5%");
  Serial.printf("- 智能映射：用户0-100%% -> 硬件0-%d%%（线性映射）\n", FAN_HARDWARE_LIMIT);
  Serial.println("PID曲线跟踪功能：");
  Serial.println("PID;ON          - 启用PID固定温度控制");
  Serial.println("PID;OFF         - 关闭PID控制");
  Serial.println("PID;GO          - PID烘焙开始");
  Serial.println("PID;STOP        - PID烘焙停止");
  Serial.println("PID;SV;目标温度  - 设置固定目标温度");
  Serial.println("PIDCURVE        - 启动PID曲线温度跟踪");
  Serial.println("AUTO            - 启动火力/风力曲线运行");
#ifdef USE_OPTIMIZED_PWM_CONTROL
  Serial.println("PWM控制测试命令：");
  Serial.println("PWMTEST         - 运行PWM控制线性度测试");
  Serial.println("PWMFREQ,频率    - 动态调整PWM频率");
  Serial.println("SMOOTHING,因子  - 调整平滑因子");
  Serial.println("RATELIMIT,限制  - 调整功率变化率限制");
  Serial.println("STABILITYTEST   - 运行功率稳定性测试");
  Serial.println("1600W发热丝专用命令：");
  Serial.println("HEATER1600      - 运行1600W发热丝完整测试");
  Serial.println("HEATERCONFIG    - 显示发热丝配置信息");
#endif
  Serial.println("========================================");

  init_fs();
  // 调用加载配置函数
  load_config();  // 从文件加载PID 参数
  
  // 初始化曲线
  init_curve();
  
  delay(1000);
}

// 主程序循环函数---------------------------------------------------------
void loop() {
  check_Serial();     // 检测普通串口命令
  check_BT_Serial();  // 检测蓝牙命令（已禁用SPP，仅处理BLE）
#ifdef TJC_UART_SCREEN
  check_TJC_Serial();  // 如果定义了陶晶驰的串口屏，则检测并响应屏发送的串口数据
  run_curve();         // 本地跑火力/风力曲线
  run_pid_curve();     // PID温度曲线跟踪
#endif
  record_curve();    // 记录曲线数据
  get_samples();     // 获取温度
  get_pot();         // 获取电位器值
  display_update();  // 刷新显示
  outPWM_update();   // 更新输出PWM
  
  // 定期发送数据到上位机
  static unsigned long last_logger_time = 0;
  if (millis() - last_logger_time > 1000) {  // 每秒发送一次数据
    logger();
    last_logger_time = millis();
  }
  // PID自动模式控制
  if (myPID.GetMode() == AUTOMATIC) {
    Input = beanTemperature;  // 使用豆温(BT)作为PID输入温度
    myPID.Compute();
    Output = constrain(Output, 0, 100);
    heaterPowerLevel = Output;
    outPWM_update();

    // 调试输出 - 显示PID控制状态
    static unsigned long last_pid_debug_time = 0;
    if (millis() - last_pid_debug_time > 5000) { // 每5秒输出一次调试信息
      last_pid_debug_time = millis();
      Serial.printf("[PID_DEBUG] 目标温度:%.2f 当前豆温:%.2f 输出火力:%d%%\n",
                    targetTemperature, beanTemperature, heaterPowerLevel);
    }
  }
  // delay(100);
}
